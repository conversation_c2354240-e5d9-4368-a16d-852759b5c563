<?xml version="1.0" encoding="UTF-8"?>
<module type="JAVA_MODULE" version="4">
  <component name="NewModuleRootManager" inherit-compiler-output="true">
    <exclude-output />
    <content url="file://$MODULE_DIR$">
      <excludeFolder url="file://$MODULE_DIR$/fiaranow_flutter/.dart_tool" />
      <excludeFolder url="file://$MODULE_DIR$/fiaranow_flutter/.fvm/flutter_sdk/bin/cache/dart-sdk/lib/_internal/js_runtime/.dart_tool" />
      <excludeFolder url="file://$MODULE_DIR$/fiaranow_flutter/.fvm/flutter_sdk/bin/cache/dart-sdk/lib/_internal/js_runtime/.pub" />
      <excludeFolder url="file://$MODULE_DIR$/fiaranow_flutter/.fvm/flutter_sdk/bin/cache/dart-sdk/lib/_internal/js_runtime/build" />
      <excludeFolder url="file://$MODULE_DIR$/fiaranow_flutter/.fvm/flutter_sdk/bin/cache/dart-sdk/lib/_internal/sdk_library_metadata/.dart_tool" />
      <excludeFolder url="file://$MODULE_DIR$/fiaranow_flutter/.fvm/flutter_sdk/bin/cache/dart-sdk/lib/_internal/sdk_library_metadata/.pub" />
      <excludeFolder url="file://$MODULE_DIR$/fiaranow_flutter/.fvm/flutter_sdk/bin/cache/dart-sdk/lib/_internal/sdk_library_metadata/build" />
      <excludeFolder url="file://$MODULE_DIR$/fiaranow_flutter/.fvm/flutter_sdk/bin/cache/dart-sdk/pkg/_macros/.dart_tool" />
      <excludeFolder url="file://$MODULE_DIR$/fiaranow_flutter/.fvm/flutter_sdk/bin/cache/dart-sdk/pkg/_macros/.pub" />
      <excludeFolder url="file://$MODULE_DIR$/fiaranow_flutter/.fvm/flutter_sdk/bin/cache/dart-sdk/pkg/_macros/build" />
      <excludeFolder url="file://$MODULE_DIR$/fiaranow_flutter/.fvm/flutter_sdk/bin/cache/pkg/flutter_gpu/.dart_tool" />
      <excludeFolder url="file://$MODULE_DIR$/fiaranow_flutter/.fvm/flutter_sdk/bin/cache/pkg/flutter_gpu/.pub" />
      <excludeFolder url="file://$MODULE_DIR$/fiaranow_flutter/.fvm/flutter_sdk/bin/cache/pkg/flutter_gpu/build" />
      <excludeFolder url="file://$MODULE_DIR$/fiaranow_flutter/.fvm/flutter_sdk/bin/cache/pkg/sky_engine/.dart_tool" />
      <excludeFolder url="file://$MODULE_DIR$/fiaranow_flutter/.fvm/flutter_sdk/bin/cache/pkg/sky_engine/.pub" />
      <excludeFolder url="file://$MODULE_DIR$/fiaranow_flutter/.fvm/flutter_sdk/bin/cache/pkg/sky_engine/build" />
      <excludeFolder url="file://$MODULE_DIR$/fiaranow_flutter/.fvm/flutter_sdk/dev/a11y_assessments/.dart_tool" />
      <excludeFolder url="file://$MODULE_DIR$/fiaranow_flutter/.fvm/flutter_sdk/dev/a11y_assessments/.pub" />
      <excludeFolder url="file://$MODULE_DIR$/fiaranow_flutter/.fvm/flutter_sdk/dev/a11y_assessments/build" />
      <excludeFolder url="file://$MODULE_DIR$/fiaranow_flutter/.fvm/flutter_sdk/dev/automated_tests/.dart_tool" />
      <excludeFolder url="file://$MODULE_DIR$/fiaranow_flutter/.fvm/flutter_sdk/dev/automated_tests/.pub" />
      <excludeFolder url="file://$MODULE_DIR$/fiaranow_flutter/.fvm/flutter_sdk/dev/automated_tests/build" />
      <excludeFolder url="file://$MODULE_DIR$/fiaranow_flutter/.fvm/flutter_sdk/dev/benchmarks/complex_layout/.dart_tool" />
      <excludeFolder url="file://$MODULE_DIR$/fiaranow_flutter/.fvm/flutter_sdk/dev/benchmarks/complex_layout/.pub" />
      <excludeFolder url="file://$MODULE_DIR$/fiaranow_flutter/.fvm/flutter_sdk/dev/benchmarks/complex_layout/build" />
      <excludeFolder url="file://$MODULE_DIR$/fiaranow_flutter/.fvm/flutter_sdk/dev/benchmarks/macrobenchmarks/.dart_tool" />
      <excludeFolder url="file://$MODULE_DIR$/fiaranow_flutter/.fvm/flutter_sdk/dev/benchmarks/macrobenchmarks/.pub" />
      <excludeFolder url="file://$MODULE_DIR$/fiaranow_flutter/.fvm/flutter_sdk/dev/benchmarks/macrobenchmarks/build" />
      <excludeFolder url="file://$MODULE_DIR$/fiaranow_flutter/.fvm/flutter_sdk/dev/benchmarks/microbenchmarks/.dart_tool" />
      <excludeFolder url="file://$MODULE_DIR$/fiaranow_flutter/.fvm/flutter_sdk/dev/benchmarks/microbenchmarks/.pub" />
      <excludeFolder url="file://$MODULE_DIR$/fiaranow_flutter/.fvm/flutter_sdk/dev/benchmarks/microbenchmarks/build" />
      <excludeFolder url="file://$MODULE_DIR$/fiaranow_flutter/.fvm/flutter_sdk/dev/benchmarks/multiple_flutters/module/.dart_tool" />
      <excludeFolder url="file://$MODULE_DIR$/fiaranow_flutter/.fvm/flutter_sdk/dev/benchmarks/multiple_flutters/module/.pub" />
      <excludeFolder url="file://$MODULE_DIR$/fiaranow_flutter/.fvm/flutter_sdk/dev/benchmarks/multiple_flutters/module/build" />
      <excludeFolder url="file://$MODULE_DIR$/fiaranow_flutter/.fvm/flutter_sdk/dev/benchmarks/platform_channels_benchmarks/.dart_tool" />
      <excludeFolder url="file://$MODULE_DIR$/fiaranow_flutter/.fvm/flutter_sdk/dev/benchmarks/platform_channels_benchmarks/.pub" />
      <excludeFolder url="file://$MODULE_DIR$/fiaranow_flutter/.fvm/flutter_sdk/dev/benchmarks/platform_channels_benchmarks/build" />
      <excludeFolder url="file://$MODULE_DIR$/fiaranow_flutter/.fvm/flutter_sdk/dev/benchmarks/platform_views_layout/.dart_tool" />
      <excludeFolder url="file://$MODULE_DIR$/fiaranow_flutter/.fvm/flutter_sdk/dev/benchmarks/platform_views_layout/.pub" />
      <excludeFolder url="file://$MODULE_DIR$/fiaranow_flutter/.fvm/flutter_sdk/dev/benchmarks/platform_views_layout/build" />
      <excludeFolder url="file://$MODULE_DIR$/fiaranow_flutter/.fvm/flutter_sdk/dev/benchmarks/platform_views_layout_hybrid_composition/.dart_tool" />
      <excludeFolder url="file://$MODULE_DIR$/fiaranow_flutter/.fvm/flutter_sdk/dev/benchmarks/platform_views_layout_hybrid_composition/.pub" />
      <excludeFolder url="file://$MODULE_DIR$/fiaranow_flutter/.fvm/flutter_sdk/dev/benchmarks/platform_views_layout_hybrid_composition/build" />
      <excludeFolder url="file://$MODULE_DIR$/fiaranow_flutter/.fvm/flutter_sdk/dev/benchmarks/test_apps/stocks/.dart_tool" />
      <excludeFolder url="file://$MODULE_DIR$/fiaranow_flutter/.fvm/flutter_sdk/dev/benchmarks/test_apps/stocks/.pub" />
      <excludeFolder url="file://$MODULE_DIR$/fiaranow_flutter/.fvm/flutter_sdk/dev/benchmarks/test_apps/stocks/build" />
      <excludeFolder url="file://$MODULE_DIR$/fiaranow_flutter/.fvm/flutter_sdk/dev/bots/.dart_tool" />
      <excludeFolder url="file://$MODULE_DIR$/fiaranow_flutter/.fvm/flutter_sdk/dev/bots/.pub" />
      <excludeFolder url="file://$MODULE_DIR$/fiaranow_flutter/.fvm/flutter_sdk/dev/bots/build" />
      <excludeFolder url="file://$MODULE_DIR$/fiaranow_flutter/.fvm/flutter_sdk/dev/conductor/core/.dart_tool" />
      <excludeFolder url="file://$MODULE_DIR$/fiaranow_flutter/.fvm/flutter_sdk/dev/conductor/core/.pub" />
      <excludeFolder url="file://$MODULE_DIR$/fiaranow_flutter/.fvm/flutter_sdk/dev/conductor/core/build" />
      <excludeFolder url="file://$MODULE_DIR$/fiaranow_flutter/.fvm/flutter_sdk/dev/customer_testing/.dart_tool" />
      <excludeFolder url="file://$MODULE_DIR$/fiaranow_flutter/.fvm/flutter_sdk/dev/customer_testing/.pub" />
      <excludeFolder url="file://$MODULE_DIR$/fiaranow_flutter/.fvm/flutter_sdk/dev/customer_testing/build" />
      <excludeFolder url="file://$MODULE_DIR$/fiaranow_flutter/.fvm/flutter_sdk/dev/devicelab/.dart_tool" />
      <excludeFolder url="file://$MODULE_DIR$/fiaranow_flutter/.fvm/flutter_sdk/dev/devicelab/.pub" />
      <excludeFolder url="file://$MODULE_DIR$/fiaranow_flutter/.fvm/flutter_sdk/dev/devicelab/build" />
      <excludeFolder url="file://$MODULE_DIR$/fiaranow_flutter/.fvm/flutter_sdk/dev/docs/platform_integration/.dart_tool" />
      <excludeFolder url="file://$MODULE_DIR$/fiaranow_flutter/.fvm/flutter_sdk/dev/docs/platform_integration/.pub" />
      <excludeFolder url="file://$MODULE_DIR$/fiaranow_flutter/.fvm/flutter_sdk/dev/docs/platform_integration/build" />
      <excludeFolder url="file://$MODULE_DIR$/fiaranow_flutter/.fvm/flutter_sdk/dev/docs/renderers/.dart_tool" />
      <excludeFolder url="file://$MODULE_DIR$/fiaranow_flutter/.fvm/flutter_sdk/dev/docs/renderers/.pub" />
      <excludeFolder url="file://$MODULE_DIR$/fiaranow_flutter/.fvm/flutter_sdk/dev/docs/renderers/build" />
      <excludeFolder url="file://$MODULE_DIR$/fiaranow_flutter/.fvm/flutter_sdk/dev/forbidden_from_release_tests/.dart_tool" />
      <excludeFolder url="file://$MODULE_DIR$/fiaranow_flutter/.fvm/flutter_sdk/dev/forbidden_from_release_tests/.pub" />
      <excludeFolder url="file://$MODULE_DIR$/fiaranow_flutter/.fvm/flutter_sdk/dev/forbidden_from_release_tests/build" />
      <excludeFolder url="file://$MODULE_DIR$/fiaranow_flutter/.fvm/flutter_sdk/dev/integration_tests/abstract_method_smoke_test/.dart_tool" />
      <excludeFolder url="file://$MODULE_DIR$/fiaranow_flutter/.fvm/flutter_sdk/dev/integration_tests/abstract_method_smoke_test/.pub" />
      <excludeFolder url="file://$MODULE_DIR$/fiaranow_flutter/.fvm/flutter_sdk/dev/integration_tests/abstract_method_smoke_test/build" />
      <excludeFolder url="file://$MODULE_DIR$/fiaranow_flutter/.fvm/flutter_sdk/dev/integration_tests/android_embedding_v2_smoke_test/.dart_tool" />
      <excludeFolder url="file://$MODULE_DIR$/fiaranow_flutter/.fvm/flutter_sdk/dev/integration_tests/android_embedding_v2_smoke_test/.pub" />
      <excludeFolder url="file://$MODULE_DIR$/fiaranow_flutter/.fvm/flutter_sdk/dev/integration_tests/android_embedding_v2_smoke_test/build" />
      <excludeFolder url="file://$MODULE_DIR$/fiaranow_flutter/.fvm/flutter_sdk/dev/integration_tests/android_semantics_testing/.dart_tool" />
      <excludeFolder url="file://$MODULE_DIR$/fiaranow_flutter/.fvm/flutter_sdk/dev/integration_tests/android_semantics_testing/.pub" />
      <excludeFolder url="file://$MODULE_DIR$/fiaranow_flutter/.fvm/flutter_sdk/dev/integration_tests/android_semantics_testing/build" />
      <excludeFolder url="file://$MODULE_DIR$/fiaranow_flutter/.fvm/flutter_sdk/dev/integration_tests/android_verified_input/.dart_tool" />
      <excludeFolder url="file://$MODULE_DIR$/fiaranow_flutter/.fvm/flutter_sdk/dev/integration_tests/android_verified_input/.pub" />
      <excludeFolder url="file://$MODULE_DIR$/fiaranow_flutter/.fvm/flutter_sdk/dev/integration_tests/android_verified_input/build" />
      <excludeFolder url="file://$MODULE_DIR$/fiaranow_flutter/.fvm/flutter_sdk/dev/integration_tests/android_views/.dart_tool" />
      <excludeFolder url="file://$MODULE_DIR$/fiaranow_flutter/.fvm/flutter_sdk/dev/integration_tests/android_views/.pub" />
      <excludeFolder url="file://$MODULE_DIR$/fiaranow_flutter/.fvm/flutter_sdk/dev/integration_tests/android_views/build" />
      <excludeFolder url="file://$MODULE_DIR$/fiaranow_flutter/.fvm/flutter_sdk/dev/integration_tests/channels/.dart_tool" />
      <excludeFolder url="file://$MODULE_DIR$/fiaranow_flutter/.fvm/flutter_sdk/dev/integration_tests/channels/.pub" />
      <excludeFolder url="file://$MODULE_DIR$/fiaranow_flutter/.fvm/flutter_sdk/dev/integration_tests/channels/build" />
      <excludeFolder url="file://$MODULE_DIR$/fiaranow_flutter/.fvm/flutter_sdk/dev/integration_tests/deferred_components_test/.dart_tool" />
      <excludeFolder url="file://$MODULE_DIR$/fiaranow_flutter/.fvm/flutter_sdk/dev/integration_tests/deferred_components_test/.pub" />
      <excludeFolder url="file://$MODULE_DIR$/fiaranow_flutter/.fvm/flutter_sdk/dev/integration_tests/deferred_components_test/build" />
      <excludeFolder url="file://$MODULE_DIR$/fiaranow_flutter/.fvm/flutter_sdk/dev/integration_tests/external_textures/.dart_tool" />
      <excludeFolder url="file://$MODULE_DIR$/fiaranow_flutter/.fvm/flutter_sdk/dev/integration_tests/external_textures/.pub" />
      <excludeFolder url="file://$MODULE_DIR$/fiaranow_flutter/.fvm/flutter_sdk/dev/integration_tests/external_textures/build" />
      <excludeFolder url="file://$MODULE_DIR$/fiaranow_flutter/.fvm/flutter_sdk/dev/integration_tests/flavors/.dart_tool" />
      <excludeFolder url="file://$MODULE_DIR$/fiaranow_flutter/.fvm/flutter_sdk/dev/integration_tests/flavors/.pub" />
      <excludeFolder url="file://$MODULE_DIR$/fiaranow_flutter/.fvm/flutter_sdk/dev/integration_tests/flavors/build" />
      <excludeFolder url="file://$MODULE_DIR$/fiaranow_flutter/.fvm/flutter_sdk/dev/integration_tests/flutter_gallery/.dart_tool" />
      <excludeFolder url="file://$MODULE_DIR$/fiaranow_flutter/.fvm/flutter_sdk/dev/integration_tests/flutter_gallery/.pub" />
      <excludeFolder url="file://$MODULE_DIR$/fiaranow_flutter/.fvm/flutter_sdk/dev/integration_tests/flutter_gallery/build" />
      <excludeFolder url="file://$MODULE_DIR$/fiaranow_flutter/.fvm/flutter_sdk/dev/integration_tests/gradle_deprecated_settings/.dart_tool" />
      <excludeFolder url="file://$MODULE_DIR$/fiaranow_flutter/.fvm/flutter_sdk/dev/integration_tests/gradle_deprecated_settings/.pub" />
      <excludeFolder url="file://$MODULE_DIR$/fiaranow_flutter/.fvm/flutter_sdk/dev/integration_tests/gradle_deprecated_settings/build" />
      <excludeFolder url="file://$MODULE_DIR$/fiaranow_flutter/.fvm/flutter_sdk/dev/integration_tests/hybrid_android_views/.dart_tool" />
      <excludeFolder url="file://$MODULE_DIR$/fiaranow_flutter/.fvm/flutter_sdk/dev/integration_tests/hybrid_android_views/.pub" />
      <excludeFolder url="file://$MODULE_DIR$/fiaranow_flutter/.fvm/flutter_sdk/dev/integration_tests/hybrid_android_views/build" />
      <excludeFolder url="file://$MODULE_DIR$/fiaranow_flutter/.fvm/flutter_sdk/dev/integration_tests/ios_add2app_life_cycle/flutterapp/.dart_tool" />
      <excludeFolder url="file://$MODULE_DIR$/fiaranow_flutter/.fvm/flutter_sdk/dev/integration_tests/ios_add2app_life_cycle/flutterapp/.pub" />
      <excludeFolder url="file://$MODULE_DIR$/fiaranow_flutter/.fvm/flutter_sdk/dev/integration_tests/ios_add2app_life_cycle/flutterapp/build" />
      <excludeFolder url="file://$MODULE_DIR$/fiaranow_flutter/.fvm/flutter_sdk/dev/integration_tests/ios_app_with_extensions/.dart_tool" />
      <excludeFolder url="file://$MODULE_DIR$/fiaranow_flutter/.fvm/flutter_sdk/dev/integration_tests/ios_app_with_extensions/.pub" />
      <excludeFolder url="file://$MODULE_DIR$/fiaranow_flutter/.fvm/flutter_sdk/dev/integration_tests/ios_app_with_extensions/build" />
      <excludeFolder url="file://$MODULE_DIR$/fiaranow_flutter/.fvm/flutter_sdk/dev/integration_tests/ios_platform_view_tests/.dart_tool" />
      <excludeFolder url="file://$MODULE_DIR$/fiaranow_flutter/.fvm/flutter_sdk/dev/integration_tests/ios_platform_view_tests/.pub" />
      <excludeFolder url="file://$MODULE_DIR$/fiaranow_flutter/.fvm/flutter_sdk/dev/integration_tests/ios_platform_view_tests/build" />
      <excludeFolder url="file://$MODULE_DIR$/fiaranow_flutter/.fvm/flutter_sdk/dev/integration_tests/link_hook/.dart_tool" />
      <excludeFolder url="file://$MODULE_DIR$/fiaranow_flutter/.fvm/flutter_sdk/dev/integration_tests/link_hook/.pub" />
      <excludeFolder url="file://$MODULE_DIR$/fiaranow_flutter/.fvm/flutter_sdk/dev/integration_tests/link_hook/build" />
      <excludeFolder url="file://$MODULE_DIR$/fiaranow_flutter/.fvm/flutter_sdk/dev/integration_tests/new_gallery/.dart_tool" />
      <excludeFolder url="file://$MODULE_DIR$/fiaranow_flutter/.fvm/flutter_sdk/dev/integration_tests/new_gallery/.pub" />
      <excludeFolder url="file://$MODULE_DIR$/fiaranow_flutter/.fvm/flutter_sdk/dev/integration_tests/new_gallery/build" />
      <excludeFolder url="file://$MODULE_DIR$/fiaranow_flutter/.fvm/flutter_sdk/dev/integration_tests/non_nullable/.dart_tool" />
      <excludeFolder url="file://$MODULE_DIR$/fiaranow_flutter/.fvm/flutter_sdk/dev/integration_tests/non_nullable/.pub" />
      <excludeFolder url="file://$MODULE_DIR$/fiaranow_flutter/.fvm/flutter_sdk/dev/integration_tests/non_nullable/build" />
      <excludeFolder url="file://$MODULE_DIR$/fiaranow_flutter/.fvm/flutter_sdk/dev/integration_tests/platform_interaction/.dart_tool" />
      <excludeFolder url="file://$MODULE_DIR$/fiaranow_flutter/.fvm/flutter_sdk/dev/integration_tests/platform_interaction/.pub" />
      <excludeFolder url="file://$MODULE_DIR$/fiaranow_flutter/.fvm/flutter_sdk/dev/integration_tests/platform_interaction/build" />
      <excludeFolder url="file://$MODULE_DIR$/fiaranow_flutter/.fvm/flutter_sdk/dev/integration_tests/release_smoke_test/.dart_tool" />
      <excludeFolder url="file://$MODULE_DIR$/fiaranow_flutter/.fvm/flutter_sdk/dev/integration_tests/release_smoke_test/.pub" />
      <excludeFolder url="file://$MODULE_DIR$/fiaranow_flutter/.fvm/flutter_sdk/dev/integration_tests/release_smoke_test/build" />
      <excludeFolder url="file://$MODULE_DIR$/fiaranow_flutter/.fvm/flutter_sdk/dev/integration_tests/spell_check/.dart_tool" />
      <excludeFolder url="file://$MODULE_DIR$/fiaranow_flutter/.fvm/flutter_sdk/dev/integration_tests/spell_check/.pub" />
      <excludeFolder url="file://$MODULE_DIR$/fiaranow_flutter/.fvm/flutter_sdk/dev/integration_tests/spell_check/build" />
      <excludeFolder url="file://$MODULE_DIR$/fiaranow_flutter/.fvm/flutter_sdk/dev/integration_tests/ui/.dart_tool" />
      <excludeFolder url="file://$MODULE_DIR$/fiaranow_flutter/.fvm/flutter_sdk/dev/integration_tests/ui/.pub" />
      <excludeFolder url="file://$MODULE_DIR$/fiaranow_flutter/.fvm/flutter_sdk/dev/integration_tests/ui/build" />
      <excludeFolder url="file://$MODULE_DIR$/fiaranow_flutter/.fvm/flutter_sdk/dev/integration_tests/web/.dart_tool" />
      <excludeFolder url="file://$MODULE_DIR$/fiaranow_flutter/.fvm/flutter_sdk/dev/integration_tests/web/.pub" />
      <excludeFolder url="file://$MODULE_DIR$/fiaranow_flutter/.fvm/flutter_sdk/dev/integration_tests/web/build" />
      <excludeFolder url="file://$MODULE_DIR$/fiaranow_flutter/.fvm/flutter_sdk/dev/integration_tests/web_compile_tests/.dart_tool" />
      <excludeFolder url="file://$MODULE_DIR$/fiaranow_flutter/.fvm/flutter_sdk/dev/integration_tests/web_compile_tests/.pub" />
      <excludeFolder url="file://$MODULE_DIR$/fiaranow_flutter/.fvm/flutter_sdk/dev/integration_tests/web_compile_tests/build" />
      <excludeFolder url="file://$MODULE_DIR$/fiaranow_flutter/.fvm/flutter_sdk/dev/integration_tests/web_e2e_tests/.dart_tool" />
      <excludeFolder url="file://$MODULE_DIR$/fiaranow_flutter/.fvm/flutter_sdk/dev/integration_tests/web_e2e_tests/.pub" />
      <excludeFolder url="file://$MODULE_DIR$/fiaranow_flutter/.fvm/flutter_sdk/dev/integration_tests/web_e2e_tests/build" />
      <excludeFolder url="file://$MODULE_DIR$/fiaranow_flutter/.fvm/flutter_sdk/dev/integration_tests/wide_gamut_test/.dart_tool" />
      <excludeFolder url="file://$MODULE_DIR$/fiaranow_flutter/.fvm/flutter_sdk/dev/integration_tests/wide_gamut_test/.pub" />
      <excludeFolder url="file://$MODULE_DIR$/fiaranow_flutter/.fvm/flutter_sdk/dev/integration_tests/wide_gamut_test/build" />
      <excludeFolder url="file://$MODULE_DIR$/fiaranow_flutter/.fvm/flutter_sdk/dev/integration_tests/windows_startup_test/.dart_tool" />
      <excludeFolder url="file://$MODULE_DIR$/fiaranow_flutter/.fvm/flutter_sdk/dev/integration_tests/windows_startup_test/.pub" />
      <excludeFolder url="file://$MODULE_DIR$/fiaranow_flutter/.fvm/flutter_sdk/dev/integration_tests/windows_startup_test/build" />
      <excludeFolder url="file://$MODULE_DIR$/fiaranow_flutter/.fvm/flutter_sdk/dev/manual_tests/.dart_tool" />
      <excludeFolder url="file://$MODULE_DIR$/fiaranow_flutter/.fvm/flutter_sdk/dev/manual_tests/.pub" />
      <excludeFolder url="file://$MODULE_DIR$/fiaranow_flutter/.fvm/flutter_sdk/dev/manual_tests/build" />
      <excludeFolder url="file://$MODULE_DIR$/fiaranow_flutter/.fvm/flutter_sdk/dev/missing_dependency_tests/.dart_tool" />
      <excludeFolder url="file://$MODULE_DIR$/fiaranow_flutter/.fvm/flutter_sdk/dev/missing_dependency_tests/.pub" />
      <excludeFolder url="file://$MODULE_DIR$/fiaranow_flutter/.fvm/flutter_sdk/dev/missing_dependency_tests/build" />
      <excludeFolder url="file://$MODULE_DIR$/fiaranow_flutter/.fvm/flutter_sdk/dev/snippets/.dart_tool" />
      <excludeFolder url="file://$MODULE_DIR$/fiaranow_flutter/.fvm/flutter_sdk/dev/snippets/.pub" />
      <excludeFolder url="file://$MODULE_DIR$/fiaranow_flutter/.fvm/flutter_sdk/dev/snippets/build" />
      <excludeFolder url="file://$MODULE_DIR$/fiaranow_flutter/.fvm/flutter_sdk/dev/tools/.dart_tool" />
      <excludeFolder url="file://$MODULE_DIR$/fiaranow_flutter/.fvm/flutter_sdk/dev/tools/.pub" />
      <excludeFolder url="file://$MODULE_DIR$/fiaranow_flutter/.fvm/flutter_sdk/dev/tools/build" />
      <excludeFolder url="file://$MODULE_DIR$/fiaranow_flutter/.fvm/flutter_sdk/dev/tools/gen_defaults/.dart_tool" />
      <excludeFolder url="file://$MODULE_DIR$/fiaranow_flutter/.fvm/flutter_sdk/dev/tools/gen_defaults/.pub" />
      <excludeFolder url="file://$MODULE_DIR$/fiaranow_flutter/.fvm/flutter_sdk/dev/tools/gen_defaults/build" />
      <excludeFolder url="file://$MODULE_DIR$/fiaranow_flutter/.fvm/flutter_sdk/dev/tools/gen_keycodes/.dart_tool" />
      <excludeFolder url="file://$MODULE_DIR$/fiaranow_flutter/.fvm/flutter_sdk/dev/tools/gen_keycodes/.pub" />
      <excludeFolder url="file://$MODULE_DIR$/fiaranow_flutter/.fvm/flutter_sdk/dev/tools/gen_keycodes/build" />
      <excludeFolder url="file://$MODULE_DIR$/fiaranow_flutter/.fvm/flutter_sdk/dev/tools/vitool/.dart_tool" />
      <excludeFolder url="file://$MODULE_DIR$/fiaranow_flutter/.fvm/flutter_sdk/dev/tools/vitool/.pub" />
      <excludeFolder url="file://$MODULE_DIR$/fiaranow_flutter/.fvm/flutter_sdk/dev/tools/vitool/build" />
      <excludeFolder url="file://$MODULE_DIR$/fiaranow_flutter/.fvm/flutter_sdk/dev/tracing_tests/.dart_tool" />
      <excludeFolder url="file://$MODULE_DIR$/fiaranow_flutter/.fvm/flutter_sdk/dev/tracing_tests/.pub" />
      <excludeFolder url="file://$MODULE_DIR$/fiaranow_flutter/.fvm/flutter_sdk/dev/tracing_tests/build" />
      <excludeFolder url="file://$MODULE_DIR$/fiaranow_flutter/.fvm/flutter_sdk/examples/api/.dart_tool" />
      <excludeFolder url="file://$MODULE_DIR$/fiaranow_flutter/.fvm/flutter_sdk/examples/api/.pub" />
      <excludeFolder url="file://$MODULE_DIR$/fiaranow_flutter/.fvm/flutter_sdk/examples/api/build" />
      <excludeFolder url="file://$MODULE_DIR$/fiaranow_flutter/.fvm/flutter_sdk/examples/flutter_view/.dart_tool" />
      <excludeFolder url="file://$MODULE_DIR$/fiaranow_flutter/.fvm/flutter_sdk/examples/flutter_view/.pub" />
      <excludeFolder url="file://$MODULE_DIR$/fiaranow_flutter/.fvm/flutter_sdk/examples/flutter_view/build" />
      <excludeFolder url="file://$MODULE_DIR$/fiaranow_flutter/.fvm/flutter_sdk/examples/hello_world/.dart_tool" />
      <excludeFolder url="file://$MODULE_DIR$/fiaranow_flutter/.fvm/flutter_sdk/examples/hello_world/.pub" />
      <excludeFolder url="file://$MODULE_DIR$/fiaranow_flutter/.fvm/flutter_sdk/examples/hello_world/build" />
      <excludeFolder url="file://$MODULE_DIR$/fiaranow_flutter/.fvm/flutter_sdk/examples/image_list/.dart_tool" />
      <excludeFolder url="file://$MODULE_DIR$/fiaranow_flutter/.fvm/flutter_sdk/examples/image_list/.pub" />
      <excludeFolder url="file://$MODULE_DIR$/fiaranow_flutter/.fvm/flutter_sdk/examples/image_list/build" />
      <excludeFolder url="file://$MODULE_DIR$/fiaranow_flutter/.fvm/flutter_sdk/examples/layers/.dart_tool" />
      <excludeFolder url="file://$MODULE_DIR$/fiaranow_flutter/.fvm/flutter_sdk/examples/layers/.pub" />
      <excludeFolder url="file://$MODULE_DIR$/fiaranow_flutter/.fvm/flutter_sdk/examples/layers/build" />
      <excludeFolder url="file://$MODULE_DIR$/fiaranow_flutter/.fvm/flutter_sdk/examples/platform_channel/.dart_tool" />
      <excludeFolder url="file://$MODULE_DIR$/fiaranow_flutter/.fvm/flutter_sdk/examples/platform_channel/.pub" />
      <excludeFolder url="file://$MODULE_DIR$/fiaranow_flutter/.fvm/flutter_sdk/examples/platform_channel/build" />
      <excludeFolder url="file://$MODULE_DIR$/fiaranow_flutter/.fvm/flutter_sdk/examples/platform_channel_swift/.dart_tool" />
      <excludeFolder url="file://$MODULE_DIR$/fiaranow_flutter/.fvm/flutter_sdk/examples/platform_channel_swift/.pub" />
      <excludeFolder url="file://$MODULE_DIR$/fiaranow_flutter/.fvm/flutter_sdk/examples/platform_channel_swift/build" />
      <excludeFolder url="file://$MODULE_DIR$/fiaranow_flutter/.fvm/flutter_sdk/examples/platform_view/.dart_tool" />
      <excludeFolder url="file://$MODULE_DIR$/fiaranow_flutter/.fvm/flutter_sdk/examples/platform_view/.pub" />
      <excludeFolder url="file://$MODULE_DIR$/fiaranow_flutter/.fvm/flutter_sdk/examples/platform_view/build" />
      <excludeFolder url="file://$MODULE_DIR$/fiaranow_flutter/.fvm/flutter_sdk/examples/splash/.dart_tool" />
      <excludeFolder url="file://$MODULE_DIR$/fiaranow_flutter/.fvm/flutter_sdk/examples/splash/.pub" />
      <excludeFolder url="file://$MODULE_DIR$/fiaranow_flutter/.fvm/flutter_sdk/examples/splash/build" />
      <excludeFolder url="file://$MODULE_DIR$/fiaranow_flutter/.fvm/flutter_sdk/examples/texture/.dart_tool" />
      <excludeFolder url="file://$MODULE_DIR$/fiaranow_flutter/.fvm/flutter_sdk/examples/texture/.pub" />
      <excludeFolder url="file://$MODULE_DIR$/fiaranow_flutter/.fvm/flutter_sdk/examples/texture/build" />
      <excludeFolder url="file://$MODULE_DIR$/fiaranow_flutter/.fvm/flutter_sdk/packages/flutter/.dart_tool" />
      <excludeFolder url="file://$MODULE_DIR$/fiaranow_flutter/.fvm/flutter_sdk/packages/flutter/.pub" />
      <excludeFolder url="file://$MODULE_DIR$/fiaranow_flutter/.fvm/flutter_sdk/packages/flutter/build" />
      <excludeFolder url="file://$MODULE_DIR$/fiaranow_flutter/.fvm/flutter_sdk/packages/flutter/test_private/.dart_tool" />
      <excludeFolder url="file://$MODULE_DIR$/fiaranow_flutter/.fvm/flutter_sdk/packages/flutter/test_private/.pub" />
      <excludeFolder url="file://$MODULE_DIR$/fiaranow_flutter/.fvm/flutter_sdk/packages/flutter/test_private/build" />
      <excludeFolder url="file://$MODULE_DIR$/fiaranow_flutter/.fvm/flutter_sdk/packages/flutter/test_private/test/.dart_tool" />
      <excludeFolder url="file://$MODULE_DIR$/fiaranow_flutter/.fvm/flutter_sdk/packages/flutter/test_private/test/.pub" />
      <excludeFolder url="file://$MODULE_DIR$/fiaranow_flutter/.fvm/flutter_sdk/packages/flutter/test_private/test/build" />
      <excludeFolder url="file://$MODULE_DIR$/fiaranow_flutter/.fvm/flutter_sdk/packages/flutter_driver/.dart_tool" />
      <excludeFolder url="file://$MODULE_DIR$/fiaranow_flutter/.fvm/flutter_sdk/packages/flutter_driver/.pub" />
      <excludeFolder url="file://$MODULE_DIR$/fiaranow_flutter/.fvm/flutter_sdk/packages/flutter_driver/build" />
      <excludeFolder url="file://$MODULE_DIR$/fiaranow_flutter/.fvm/flutter_sdk/packages/flutter_goldens/.dart_tool" />
      <excludeFolder url="file://$MODULE_DIR$/fiaranow_flutter/.fvm/flutter_sdk/packages/flutter_goldens/.pub" />
      <excludeFolder url="file://$MODULE_DIR$/fiaranow_flutter/.fvm/flutter_sdk/packages/flutter_goldens/build" />
      <excludeFolder url="file://$MODULE_DIR$/fiaranow_flutter/.fvm/flutter_sdk/packages/flutter_localizations/.dart_tool" />
      <excludeFolder url="file://$MODULE_DIR$/fiaranow_flutter/.fvm/flutter_sdk/packages/flutter_localizations/.pub" />
      <excludeFolder url="file://$MODULE_DIR$/fiaranow_flutter/.fvm/flutter_sdk/packages/flutter_localizations/build" />
      <excludeFolder url="file://$MODULE_DIR$/fiaranow_flutter/.fvm/flutter_sdk/packages/flutter_test/.dart_tool" />
      <excludeFolder url="file://$MODULE_DIR$/fiaranow_flutter/.fvm/flutter_sdk/packages/flutter_test/.pub" />
      <excludeFolder url="file://$MODULE_DIR$/fiaranow_flutter/.fvm/flutter_sdk/packages/flutter_test/build" />
      <excludeFolder url="file://$MODULE_DIR$/fiaranow_flutter/.fvm/flutter_sdk/packages/flutter_test/test/test_config/project_root/.dart_tool" />
      <excludeFolder url="file://$MODULE_DIR$/fiaranow_flutter/.fvm/flutter_sdk/packages/flutter_test/test/test_config/project_root/.pub" />
      <excludeFolder url="file://$MODULE_DIR$/fiaranow_flutter/.fvm/flutter_sdk/packages/flutter_test/test/test_config/project_root/build" />
      <excludeFolder url="file://$MODULE_DIR$/fiaranow_flutter/.fvm/flutter_sdk/packages/flutter_tools/.dart_tool" />
      <excludeFolder url="file://$MODULE_DIR$/fiaranow_flutter/.fvm/flutter_sdk/packages/flutter_tools/.pub" />
      <excludeFolder url="file://$MODULE_DIR$/fiaranow_flutter/.fvm/flutter_sdk/packages/flutter_tools/build" />
      <excludeFolder url="file://$MODULE_DIR$/fiaranow_flutter/.fvm/flutter_sdk/packages/flutter_web_plugins/.dart_tool" />
      <excludeFolder url="file://$MODULE_DIR$/fiaranow_flutter/.fvm/flutter_sdk/packages/flutter_web_plugins/.pub" />
      <excludeFolder url="file://$MODULE_DIR$/fiaranow_flutter/.fvm/flutter_sdk/packages/flutter_web_plugins/build" />
      <excludeFolder url="file://$MODULE_DIR$/fiaranow_flutter/.fvm/flutter_sdk/packages/fuchsia_remote_debug_protocol/.dart_tool" />
      <excludeFolder url="file://$MODULE_DIR$/fiaranow_flutter/.fvm/flutter_sdk/packages/fuchsia_remote_debug_protocol/.pub" />
      <excludeFolder url="file://$MODULE_DIR$/fiaranow_flutter/.fvm/flutter_sdk/packages/fuchsia_remote_debug_protocol/build" />
      <excludeFolder url="file://$MODULE_DIR$/fiaranow_flutter/.fvm/flutter_sdk/packages/integration_test/.dart_tool" />
      <excludeFolder url="file://$MODULE_DIR$/fiaranow_flutter/.fvm/flutter_sdk/packages/integration_test/.pub" />
      <excludeFolder url="file://$MODULE_DIR$/fiaranow_flutter/.fvm/flutter_sdk/packages/integration_test/build" />
      <excludeFolder url="file://$MODULE_DIR$/fiaranow_flutter/.fvm/flutter_sdk/packages/integration_test/example/.dart_tool" />
      <excludeFolder url="file://$MODULE_DIR$/fiaranow_flutter/.fvm/flutter_sdk/packages/integration_test/example/.pub" />
      <excludeFolder url="file://$MODULE_DIR$/fiaranow_flutter/.fvm/flutter_sdk/packages/integration_test/example/build" />
      <excludeFolder url="file://$MODULE_DIR$/fiaranow_flutter/.fvm/flutter_sdk/packages/integration_test/integration_test_macos/.dart_tool" />
      <excludeFolder url="file://$MODULE_DIR$/fiaranow_flutter/.fvm/flutter_sdk/packages/integration_test/integration_test_macos/.pub" />
      <excludeFolder url="file://$MODULE_DIR$/fiaranow_flutter/.fvm/flutter_sdk/packages/integration_test/integration_test_macos/build" />
      <excludeFolder url="file://$MODULE_DIR$/fiaranow_flutter/.fvm/versions/3.24.4/bin/cache/dart-sdk/lib/_internal/js_runtime/.dart_tool" />
      <excludeFolder url="file://$MODULE_DIR$/fiaranow_flutter/.fvm/versions/3.24.4/bin/cache/dart-sdk/lib/_internal/js_runtime/.pub" />
      <excludeFolder url="file://$MODULE_DIR$/fiaranow_flutter/.fvm/versions/3.24.4/bin/cache/dart-sdk/lib/_internal/js_runtime/build" />
      <excludeFolder url="file://$MODULE_DIR$/fiaranow_flutter/.fvm/versions/3.24.4/bin/cache/dart-sdk/lib/_internal/sdk_library_metadata/.dart_tool" />
      <excludeFolder url="file://$MODULE_DIR$/fiaranow_flutter/.fvm/versions/3.24.4/bin/cache/dart-sdk/lib/_internal/sdk_library_metadata/.pub" />
      <excludeFolder url="file://$MODULE_DIR$/fiaranow_flutter/.fvm/versions/3.24.4/bin/cache/dart-sdk/lib/_internal/sdk_library_metadata/build" />
      <excludeFolder url="file://$MODULE_DIR$/fiaranow_flutter/.fvm/versions/3.24.4/bin/cache/dart-sdk/pkg/_macros/.dart_tool" />
      <excludeFolder url="file://$MODULE_DIR$/fiaranow_flutter/.fvm/versions/3.24.4/bin/cache/dart-sdk/pkg/_macros/.pub" />
      <excludeFolder url="file://$MODULE_DIR$/fiaranow_flutter/.fvm/versions/3.24.4/bin/cache/dart-sdk/pkg/_macros/build" />
      <excludeFolder url="file://$MODULE_DIR$/fiaranow_flutter/.fvm/versions/3.24.4/bin/cache/pkg/flutter_gpu/.dart_tool" />
      <excludeFolder url="file://$MODULE_DIR$/fiaranow_flutter/.fvm/versions/3.24.4/bin/cache/pkg/flutter_gpu/.pub" />
      <excludeFolder url="file://$MODULE_DIR$/fiaranow_flutter/.fvm/versions/3.24.4/bin/cache/pkg/flutter_gpu/build" />
      <excludeFolder url="file://$MODULE_DIR$/fiaranow_flutter/.fvm/versions/3.24.4/bin/cache/pkg/sky_engine/.dart_tool" />
      <excludeFolder url="file://$MODULE_DIR$/fiaranow_flutter/.fvm/versions/3.24.4/bin/cache/pkg/sky_engine/.pub" />
      <excludeFolder url="file://$MODULE_DIR$/fiaranow_flutter/.fvm/versions/3.24.4/bin/cache/pkg/sky_engine/build" />
      <excludeFolder url="file://$MODULE_DIR$/fiaranow_flutter/.fvm/versions/3.24.4/dev/a11y_assessments/.dart_tool" />
      <excludeFolder url="file://$MODULE_DIR$/fiaranow_flutter/.fvm/versions/3.24.4/dev/a11y_assessments/.pub" />
      <excludeFolder url="file://$MODULE_DIR$/fiaranow_flutter/.fvm/versions/3.24.4/dev/a11y_assessments/build" />
      <excludeFolder url="file://$MODULE_DIR$/fiaranow_flutter/.fvm/versions/3.24.4/dev/automated_tests/.dart_tool" />
      <excludeFolder url="file://$MODULE_DIR$/fiaranow_flutter/.fvm/versions/3.24.4/dev/automated_tests/.pub" />
      <excludeFolder url="file://$MODULE_DIR$/fiaranow_flutter/.fvm/versions/3.24.4/dev/automated_tests/build" />
      <excludeFolder url="file://$MODULE_DIR$/fiaranow_flutter/.fvm/versions/3.24.4/dev/benchmarks/complex_layout/.dart_tool" />
      <excludeFolder url="file://$MODULE_DIR$/fiaranow_flutter/.fvm/versions/3.24.4/dev/benchmarks/complex_layout/.pub" />
      <excludeFolder url="file://$MODULE_DIR$/fiaranow_flutter/.fvm/versions/3.24.4/dev/benchmarks/complex_layout/build" />
      <excludeFolder url="file://$MODULE_DIR$/fiaranow_flutter/.fvm/versions/3.24.4/dev/benchmarks/macrobenchmarks/.dart_tool" />
      <excludeFolder url="file://$MODULE_DIR$/fiaranow_flutter/.fvm/versions/3.24.4/dev/benchmarks/macrobenchmarks/.pub" />
      <excludeFolder url="file://$MODULE_DIR$/fiaranow_flutter/.fvm/versions/3.24.4/dev/benchmarks/macrobenchmarks/build" />
      <excludeFolder url="file://$MODULE_DIR$/fiaranow_flutter/.fvm/versions/3.24.4/dev/benchmarks/microbenchmarks/.dart_tool" />
      <excludeFolder url="file://$MODULE_DIR$/fiaranow_flutter/.fvm/versions/3.24.4/dev/benchmarks/microbenchmarks/.pub" />
      <excludeFolder url="file://$MODULE_DIR$/fiaranow_flutter/.fvm/versions/3.24.4/dev/benchmarks/microbenchmarks/build" />
      <excludeFolder url="file://$MODULE_DIR$/fiaranow_flutter/.fvm/versions/3.24.4/dev/benchmarks/multiple_flutters/module/.dart_tool" />
      <excludeFolder url="file://$MODULE_DIR$/fiaranow_flutter/.fvm/versions/3.24.4/dev/benchmarks/multiple_flutters/module/.pub" />
      <excludeFolder url="file://$MODULE_DIR$/fiaranow_flutter/.fvm/versions/3.24.4/dev/benchmarks/multiple_flutters/module/build" />
      <excludeFolder url="file://$MODULE_DIR$/fiaranow_flutter/.fvm/versions/3.24.4/dev/benchmarks/platform_channels_benchmarks/.dart_tool" />
      <excludeFolder url="file://$MODULE_DIR$/fiaranow_flutter/.fvm/versions/3.24.4/dev/benchmarks/platform_channels_benchmarks/.pub" />
      <excludeFolder url="file://$MODULE_DIR$/fiaranow_flutter/.fvm/versions/3.24.4/dev/benchmarks/platform_channels_benchmarks/build" />
      <excludeFolder url="file://$MODULE_DIR$/fiaranow_flutter/.fvm/versions/3.24.4/dev/benchmarks/platform_views_layout/.dart_tool" />
      <excludeFolder url="file://$MODULE_DIR$/fiaranow_flutter/.fvm/versions/3.24.4/dev/benchmarks/platform_views_layout/.pub" />
      <excludeFolder url="file://$MODULE_DIR$/fiaranow_flutter/.fvm/versions/3.24.4/dev/benchmarks/platform_views_layout/build" />
      <excludeFolder url="file://$MODULE_DIR$/fiaranow_flutter/.fvm/versions/3.24.4/dev/benchmarks/platform_views_layout_hybrid_composition/.dart_tool" />
      <excludeFolder url="file://$MODULE_DIR$/fiaranow_flutter/.fvm/versions/3.24.4/dev/benchmarks/platform_views_layout_hybrid_composition/.pub" />
      <excludeFolder url="file://$MODULE_DIR$/fiaranow_flutter/.fvm/versions/3.24.4/dev/benchmarks/platform_views_layout_hybrid_composition/build" />
      <excludeFolder url="file://$MODULE_DIR$/fiaranow_flutter/.fvm/versions/3.24.4/dev/benchmarks/test_apps/stocks/.dart_tool" />
      <excludeFolder url="file://$MODULE_DIR$/fiaranow_flutter/.fvm/versions/3.24.4/dev/benchmarks/test_apps/stocks/.pub" />
      <excludeFolder url="file://$MODULE_DIR$/fiaranow_flutter/.fvm/versions/3.24.4/dev/benchmarks/test_apps/stocks/build" />
      <excludeFolder url="file://$MODULE_DIR$/fiaranow_flutter/.fvm/versions/3.24.4/dev/bots/.dart_tool" />
      <excludeFolder url="file://$MODULE_DIR$/fiaranow_flutter/.fvm/versions/3.24.4/dev/bots/.pub" />
      <excludeFolder url="file://$MODULE_DIR$/fiaranow_flutter/.fvm/versions/3.24.4/dev/bots/build" />
      <excludeFolder url="file://$MODULE_DIR$/fiaranow_flutter/.fvm/versions/3.24.4/dev/conductor/core/.dart_tool" />
      <excludeFolder url="file://$MODULE_DIR$/fiaranow_flutter/.fvm/versions/3.24.4/dev/conductor/core/.pub" />
      <excludeFolder url="file://$MODULE_DIR$/fiaranow_flutter/.fvm/versions/3.24.4/dev/conductor/core/build" />
      <excludeFolder url="file://$MODULE_DIR$/fiaranow_flutter/.fvm/versions/3.24.4/dev/customer_testing/.dart_tool" />
      <excludeFolder url="file://$MODULE_DIR$/fiaranow_flutter/.fvm/versions/3.24.4/dev/customer_testing/.pub" />
      <excludeFolder url="file://$MODULE_DIR$/fiaranow_flutter/.fvm/versions/3.24.4/dev/customer_testing/build" />
      <excludeFolder url="file://$MODULE_DIR$/fiaranow_flutter/.fvm/versions/3.24.4/dev/devicelab/.dart_tool" />
      <excludeFolder url="file://$MODULE_DIR$/fiaranow_flutter/.fvm/versions/3.24.4/dev/devicelab/.pub" />
      <excludeFolder url="file://$MODULE_DIR$/fiaranow_flutter/.fvm/versions/3.24.4/dev/devicelab/build" />
      <excludeFolder url="file://$MODULE_DIR$/fiaranow_flutter/.fvm/versions/3.24.4/dev/docs/platform_integration/.dart_tool" />
      <excludeFolder url="file://$MODULE_DIR$/fiaranow_flutter/.fvm/versions/3.24.4/dev/docs/platform_integration/.pub" />
      <excludeFolder url="file://$MODULE_DIR$/fiaranow_flutter/.fvm/versions/3.24.4/dev/docs/platform_integration/build" />
      <excludeFolder url="file://$MODULE_DIR$/fiaranow_flutter/.fvm/versions/3.24.4/dev/docs/renderers/.dart_tool" />
      <excludeFolder url="file://$MODULE_DIR$/fiaranow_flutter/.fvm/versions/3.24.4/dev/docs/renderers/.pub" />
      <excludeFolder url="file://$MODULE_DIR$/fiaranow_flutter/.fvm/versions/3.24.4/dev/docs/renderers/build" />
      <excludeFolder url="file://$MODULE_DIR$/fiaranow_flutter/.fvm/versions/3.24.4/dev/forbidden_from_release_tests/.dart_tool" />
      <excludeFolder url="file://$MODULE_DIR$/fiaranow_flutter/.fvm/versions/3.24.4/dev/forbidden_from_release_tests/.pub" />
      <excludeFolder url="file://$MODULE_DIR$/fiaranow_flutter/.fvm/versions/3.24.4/dev/forbidden_from_release_tests/build" />
      <excludeFolder url="file://$MODULE_DIR$/fiaranow_flutter/.fvm/versions/3.24.4/dev/integration_tests/abstract_method_smoke_test/.dart_tool" />
      <excludeFolder url="file://$MODULE_DIR$/fiaranow_flutter/.fvm/versions/3.24.4/dev/integration_tests/abstract_method_smoke_test/.pub" />
      <excludeFolder url="file://$MODULE_DIR$/fiaranow_flutter/.fvm/versions/3.24.4/dev/integration_tests/abstract_method_smoke_test/build" />
      <excludeFolder url="file://$MODULE_DIR$/fiaranow_flutter/.fvm/versions/3.24.4/dev/integration_tests/android_embedding_v2_smoke_test/.dart_tool" />
      <excludeFolder url="file://$MODULE_DIR$/fiaranow_flutter/.fvm/versions/3.24.4/dev/integration_tests/android_embedding_v2_smoke_test/.pub" />
      <excludeFolder url="file://$MODULE_DIR$/fiaranow_flutter/.fvm/versions/3.24.4/dev/integration_tests/android_embedding_v2_smoke_test/build" />
      <excludeFolder url="file://$MODULE_DIR$/fiaranow_flutter/.fvm/versions/3.24.4/dev/integration_tests/android_semantics_testing/.dart_tool" />
      <excludeFolder url="file://$MODULE_DIR$/fiaranow_flutter/.fvm/versions/3.24.4/dev/integration_tests/android_semantics_testing/.pub" />
      <excludeFolder url="file://$MODULE_DIR$/fiaranow_flutter/.fvm/versions/3.24.4/dev/integration_tests/android_semantics_testing/build" />
      <excludeFolder url="file://$MODULE_DIR$/fiaranow_flutter/.fvm/versions/3.24.4/dev/integration_tests/android_verified_input/.dart_tool" />
      <excludeFolder url="file://$MODULE_DIR$/fiaranow_flutter/.fvm/versions/3.24.4/dev/integration_tests/android_verified_input/.pub" />
      <excludeFolder url="file://$MODULE_DIR$/fiaranow_flutter/.fvm/versions/3.24.4/dev/integration_tests/android_verified_input/build" />
      <excludeFolder url="file://$MODULE_DIR$/fiaranow_flutter/.fvm/versions/3.24.4/dev/integration_tests/android_views/.dart_tool" />
      <excludeFolder url="file://$MODULE_DIR$/fiaranow_flutter/.fvm/versions/3.24.4/dev/integration_tests/android_views/.pub" />
      <excludeFolder url="file://$MODULE_DIR$/fiaranow_flutter/.fvm/versions/3.24.4/dev/integration_tests/android_views/build" />
      <excludeFolder url="file://$MODULE_DIR$/fiaranow_flutter/.fvm/versions/3.24.4/dev/integration_tests/channels/.dart_tool" />
      <excludeFolder url="file://$MODULE_DIR$/fiaranow_flutter/.fvm/versions/3.24.4/dev/integration_tests/channels/.pub" />
      <excludeFolder url="file://$MODULE_DIR$/fiaranow_flutter/.fvm/versions/3.24.4/dev/integration_tests/channels/build" />
      <excludeFolder url="file://$MODULE_DIR$/fiaranow_flutter/.fvm/versions/3.24.4/dev/integration_tests/deferred_components_test/.dart_tool" />
      <excludeFolder url="file://$MODULE_DIR$/fiaranow_flutter/.fvm/versions/3.24.4/dev/integration_tests/deferred_components_test/.pub" />
      <excludeFolder url="file://$MODULE_DIR$/fiaranow_flutter/.fvm/versions/3.24.4/dev/integration_tests/deferred_components_test/build" />
      <excludeFolder url="file://$MODULE_DIR$/fiaranow_flutter/.fvm/versions/3.24.4/dev/integration_tests/external_textures/.dart_tool" />
      <excludeFolder url="file://$MODULE_DIR$/fiaranow_flutter/.fvm/versions/3.24.4/dev/integration_tests/external_textures/.pub" />
      <excludeFolder url="file://$MODULE_DIR$/fiaranow_flutter/.fvm/versions/3.24.4/dev/integration_tests/external_textures/build" />
      <excludeFolder url="file://$MODULE_DIR$/fiaranow_flutter/.fvm/versions/3.24.4/dev/integration_tests/flavors/.dart_tool" />
      <excludeFolder url="file://$MODULE_DIR$/fiaranow_flutter/.fvm/versions/3.24.4/dev/integration_tests/flavors/.pub" />
      <excludeFolder url="file://$MODULE_DIR$/fiaranow_flutter/.fvm/versions/3.24.4/dev/integration_tests/flavors/build" />
      <excludeFolder url="file://$MODULE_DIR$/fiaranow_flutter/.fvm/versions/3.24.4/dev/integration_tests/flutter_gallery/.dart_tool" />
      <excludeFolder url="file://$MODULE_DIR$/fiaranow_flutter/.fvm/versions/3.24.4/dev/integration_tests/flutter_gallery/.pub" />
      <excludeFolder url="file://$MODULE_DIR$/fiaranow_flutter/.fvm/versions/3.24.4/dev/integration_tests/flutter_gallery/build" />
      <excludeFolder url="file://$MODULE_DIR$/fiaranow_flutter/.fvm/versions/3.24.4/dev/integration_tests/gradle_deprecated_settings/.dart_tool" />
      <excludeFolder url="file://$MODULE_DIR$/fiaranow_flutter/.fvm/versions/3.24.4/dev/integration_tests/gradle_deprecated_settings/.pub" />
      <excludeFolder url="file://$MODULE_DIR$/fiaranow_flutter/.fvm/versions/3.24.4/dev/integration_tests/gradle_deprecated_settings/build" />
      <excludeFolder url="file://$MODULE_DIR$/fiaranow_flutter/.fvm/versions/3.24.4/dev/integration_tests/hybrid_android_views/.dart_tool" />
      <excludeFolder url="file://$MODULE_DIR$/fiaranow_flutter/.fvm/versions/3.24.4/dev/integration_tests/hybrid_android_views/.pub" />
      <excludeFolder url="file://$MODULE_DIR$/fiaranow_flutter/.fvm/versions/3.24.4/dev/integration_tests/hybrid_android_views/build" />
      <excludeFolder url="file://$MODULE_DIR$/fiaranow_flutter/.fvm/versions/3.24.4/dev/integration_tests/ios_add2app_life_cycle/flutterapp/.dart_tool" />
      <excludeFolder url="file://$MODULE_DIR$/fiaranow_flutter/.fvm/versions/3.24.4/dev/integration_tests/ios_add2app_life_cycle/flutterapp/.pub" />
      <excludeFolder url="file://$MODULE_DIR$/fiaranow_flutter/.fvm/versions/3.24.4/dev/integration_tests/ios_add2app_life_cycle/flutterapp/build" />
      <excludeFolder url="file://$MODULE_DIR$/fiaranow_flutter/.fvm/versions/3.24.4/dev/integration_tests/ios_app_with_extensions/.dart_tool" />
      <excludeFolder url="file://$MODULE_DIR$/fiaranow_flutter/.fvm/versions/3.24.4/dev/integration_tests/ios_app_with_extensions/.pub" />
      <excludeFolder url="file://$MODULE_DIR$/fiaranow_flutter/.fvm/versions/3.24.4/dev/integration_tests/ios_app_with_extensions/build" />
      <excludeFolder url="file://$MODULE_DIR$/fiaranow_flutter/.fvm/versions/3.24.4/dev/integration_tests/ios_platform_view_tests/.dart_tool" />
      <excludeFolder url="file://$MODULE_DIR$/fiaranow_flutter/.fvm/versions/3.24.4/dev/integration_tests/ios_platform_view_tests/.pub" />
      <excludeFolder url="file://$MODULE_DIR$/fiaranow_flutter/.fvm/versions/3.24.4/dev/integration_tests/ios_platform_view_tests/build" />
      <excludeFolder url="file://$MODULE_DIR$/fiaranow_flutter/.fvm/versions/3.24.4/dev/integration_tests/link_hook/.dart_tool" />
      <excludeFolder url="file://$MODULE_DIR$/fiaranow_flutter/.fvm/versions/3.24.4/dev/integration_tests/link_hook/.pub" />
      <excludeFolder url="file://$MODULE_DIR$/fiaranow_flutter/.fvm/versions/3.24.4/dev/integration_tests/link_hook/build" />
      <excludeFolder url="file://$MODULE_DIR$/fiaranow_flutter/.fvm/versions/3.24.4/dev/integration_tests/new_gallery/.dart_tool" />
      <excludeFolder url="file://$MODULE_DIR$/fiaranow_flutter/.fvm/versions/3.24.4/dev/integration_tests/new_gallery/.pub" />
      <excludeFolder url="file://$MODULE_DIR$/fiaranow_flutter/.fvm/versions/3.24.4/dev/integration_tests/new_gallery/build" />
      <excludeFolder url="file://$MODULE_DIR$/fiaranow_flutter/.fvm/versions/3.24.4/dev/integration_tests/non_nullable/.dart_tool" />
      <excludeFolder url="file://$MODULE_DIR$/fiaranow_flutter/.fvm/versions/3.24.4/dev/integration_tests/non_nullable/.pub" />
      <excludeFolder url="file://$MODULE_DIR$/fiaranow_flutter/.fvm/versions/3.24.4/dev/integration_tests/non_nullable/build" />
      <excludeFolder url="file://$MODULE_DIR$/fiaranow_flutter/.fvm/versions/3.24.4/dev/integration_tests/platform_interaction/.dart_tool" />
      <excludeFolder url="file://$MODULE_DIR$/fiaranow_flutter/.fvm/versions/3.24.4/dev/integration_tests/platform_interaction/.pub" />
      <excludeFolder url="file://$MODULE_DIR$/fiaranow_flutter/.fvm/versions/3.24.4/dev/integration_tests/platform_interaction/build" />
      <excludeFolder url="file://$MODULE_DIR$/fiaranow_flutter/.fvm/versions/3.24.4/dev/integration_tests/release_smoke_test/.dart_tool" />
      <excludeFolder url="file://$MODULE_DIR$/fiaranow_flutter/.fvm/versions/3.24.4/dev/integration_tests/release_smoke_test/.pub" />
      <excludeFolder url="file://$MODULE_DIR$/fiaranow_flutter/.fvm/versions/3.24.4/dev/integration_tests/release_smoke_test/build" />
      <excludeFolder url="file://$MODULE_DIR$/fiaranow_flutter/.fvm/versions/3.24.4/dev/integration_tests/spell_check/.dart_tool" />
      <excludeFolder url="file://$MODULE_DIR$/fiaranow_flutter/.fvm/versions/3.24.4/dev/integration_tests/spell_check/.pub" />
      <excludeFolder url="file://$MODULE_DIR$/fiaranow_flutter/.fvm/versions/3.24.4/dev/integration_tests/spell_check/build" />
      <excludeFolder url="file://$MODULE_DIR$/fiaranow_flutter/.fvm/versions/3.24.4/dev/integration_tests/ui/.dart_tool" />
      <excludeFolder url="file://$MODULE_DIR$/fiaranow_flutter/.fvm/versions/3.24.4/dev/integration_tests/ui/.pub" />
      <excludeFolder url="file://$MODULE_DIR$/fiaranow_flutter/.fvm/versions/3.24.4/dev/integration_tests/ui/build" />
      <excludeFolder url="file://$MODULE_DIR$/fiaranow_flutter/.fvm/versions/3.24.4/dev/integration_tests/web/.dart_tool" />
      <excludeFolder url="file://$MODULE_DIR$/fiaranow_flutter/.fvm/versions/3.24.4/dev/integration_tests/web/.pub" />
      <excludeFolder url="file://$MODULE_DIR$/fiaranow_flutter/.fvm/versions/3.24.4/dev/integration_tests/web/build" />
      <excludeFolder url="file://$MODULE_DIR$/fiaranow_flutter/.fvm/versions/3.24.4/dev/integration_tests/web_compile_tests/.dart_tool" />
      <excludeFolder url="file://$MODULE_DIR$/fiaranow_flutter/.fvm/versions/3.24.4/dev/integration_tests/web_compile_tests/.pub" />
      <excludeFolder url="file://$MODULE_DIR$/fiaranow_flutter/.fvm/versions/3.24.4/dev/integration_tests/web_compile_tests/build" />
      <excludeFolder url="file://$MODULE_DIR$/fiaranow_flutter/.fvm/versions/3.24.4/dev/integration_tests/web_e2e_tests/.dart_tool" />
      <excludeFolder url="file://$MODULE_DIR$/fiaranow_flutter/.fvm/versions/3.24.4/dev/integration_tests/web_e2e_tests/.pub" />
      <excludeFolder url="file://$MODULE_DIR$/fiaranow_flutter/.fvm/versions/3.24.4/dev/integration_tests/web_e2e_tests/build" />
      <excludeFolder url="file://$MODULE_DIR$/fiaranow_flutter/.fvm/versions/3.24.4/dev/integration_tests/wide_gamut_test/.dart_tool" />
      <excludeFolder url="file://$MODULE_DIR$/fiaranow_flutter/.fvm/versions/3.24.4/dev/integration_tests/wide_gamut_test/.pub" />
      <excludeFolder url="file://$MODULE_DIR$/fiaranow_flutter/.fvm/versions/3.24.4/dev/integration_tests/wide_gamut_test/build" />
      <excludeFolder url="file://$MODULE_DIR$/fiaranow_flutter/.fvm/versions/3.24.4/dev/integration_tests/windows_startup_test/.dart_tool" />
      <excludeFolder url="file://$MODULE_DIR$/fiaranow_flutter/.fvm/versions/3.24.4/dev/integration_tests/windows_startup_test/.pub" />
      <excludeFolder url="file://$MODULE_DIR$/fiaranow_flutter/.fvm/versions/3.24.4/dev/integration_tests/windows_startup_test/build" />
      <excludeFolder url="file://$MODULE_DIR$/fiaranow_flutter/.fvm/versions/3.24.4/dev/manual_tests/.dart_tool" />
      <excludeFolder url="file://$MODULE_DIR$/fiaranow_flutter/.fvm/versions/3.24.4/dev/manual_tests/.pub" />
      <excludeFolder url="file://$MODULE_DIR$/fiaranow_flutter/.fvm/versions/3.24.4/dev/manual_tests/build" />
      <excludeFolder url="file://$MODULE_DIR$/fiaranow_flutter/.fvm/versions/3.24.4/dev/missing_dependency_tests/.dart_tool" />
      <excludeFolder url="file://$MODULE_DIR$/fiaranow_flutter/.fvm/versions/3.24.4/dev/missing_dependency_tests/.pub" />
      <excludeFolder url="file://$MODULE_DIR$/fiaranow_flutter/.fvm/versions/3.24.4/dev/missing_dependency_tests/build" />
      <excludeFolder url="file://$MODULE_DIR$/fiaranow_flutter/.fvm/versions/3.24.4/dev/snippets/.dart_tool" />
      <excludeFolder url="file://$MODULE_DIR$/fiaranow_flutter/.fvm/versions/3.24.4/dev/snippets/.pub" />
      <excludeFolder url="file://$MODULE_DIR$/fiaranow_flutter/.fvm/versions/3.24.4/dev/snippets/build" />
      <excludeFolder url="file://$MODULE_DIR$/fiaranow_flutter/.fvm/versions/3.24.4/dev/tools/.dart_tool" />
      <excludeFolder url="file://$MODULE_DIR$/fiaranow_flutter/.fvm/versions/3.24.4/dev/tools/.pub" />
      <excludeFolder url="file://$MODULE_DIR$/fiaranow_flutter/.fvm/versions/3.24.4/dev/tools/build" />
      <excludeFolder url="file://$MODULE_DIR$/fiaranow_flutter/.fvm/versions/3.24.4/dev/tools/gen_defaults/.dart_tool" />
      <excludeFolder url="file://$MODULE_DIR$/fiaranow_flutter/.fvm/versions/3.24.4/dev/tools/gen_defaults/.pub" />
      <excludeFolder url="file://$MODULE_DIR$/fiaranow_flutter/.fvm/versions/3.24.4/dev/tools/gen_defaults/build" />
      <excludeFolder url="file://$MODULE_DIR$/fiaranow_flutter/.fvm/versions/3.24.4/dev/tools/gen_keycodes/.dart_tool" />
      <excludeFolder url="file://$MODULE_DIR$/fiaranow_flutter/.fvm/versions/3.24.4/dev/tools/gen_keycodes/.pub" />
      <excludeFolder url="file://$MODULE_DIR$/fiaranow_flutter/.fvm/versions/3.24.4/dev/tools/gen_keycodes/build" />
      <excludeFolder url="file://$MODULE_DIR$/fiaranow_flutter/.fvm/versions/3.24.4/dev/tools/vitool/.dart_tool" />
      <excludeFolder url="file://$MODULE_DIR$/fiaranow_flutter/.fvm/versions/3.24.4/dev/tools/vitool/.pub" />
      <excludeFolder url="file://$MODULE_DIR$/fiaranow_flutter/.fvm/versions/3.24.4/dev/tools/vitool/build" />
      <excludeFolder url="file://$MODULE_DIR$/fiaranow_flutter/.fvm/versions/3.24.4/dev/tracing_tests/.dart_tool" />
      <excludeFolder url="file://$MODULE_DIR$/fiaranow_flutter/.fvm/versions/3.24.4/dev/tracing_tests/.pub" />
      <excludeFolder url="file://$MODULE_DIR$/fiaranow_flutter/.fvm/versions/3.24.4/dev/tracing_tests/build" />
      <excludeFolder url="file://$MODULE_DIR$/fiaranow_flutter/.fvm/versions/3.24.4/examples/api/.dart_tool" />
      <excludeFolder url="file://$MODULE_DIR$/fiaranow_flutter/.fvm/versions/3.24.4/examples/api/.pub" />
      <excludeFolder url="file://$MODULE_DIR$/fiaranow_flutter/.fvm/versions/3.24.4/examples/api/build" />
      <excludeFolder url="file://$MODULE_DIR$/fiaranow_flutter/.fvm/versions/3.24.4/examples/flutter_view/.dart_tool" />
      <excludeFolder url="file://$MODULE_DIR$/fiaranow_flutter/.fvm/versions/3.24.4/examples/flutter_view/.pub" />
      <excludeFolder url="file://$MODULE_DIR$/fiaranow_flutter/.fvm/versions/3.24.4/examples/flutter_view/build" />
      <excludeFolder url="file://$MODULE_DIR$/fiaranow_flutter/.fvm/versions/3.24.4/examples/hello_world/.dart_tool" />
      <excludeFolder url="file://$MODULE_DIR$/fiaranow_flutter/.fvm/versions/3.24.4/examples/hello_world/.pub" />
      <excludeFolder url="file://$MODULE_DIR$/fiaranow_flutter/.fvm/versions/3.24.4/examples/hello_world/build" />
      <excludeFolder url="file://$MODULE_DIR$/fiaranow_flutter/.fvm/versions/3.24.4/examples/image_list/.dart_tool" />
      <excludeFolder url="file://$MODULE_DIR$/fiaranow_flutter/.fvm/versions/3.24.4/examples/image_list/.pub" />
      <excludeFolder url="file://$MODULE_DIR$/fiaranow_flutter/.fvm/versions/3.24.4/examples/image_list/build" />
      <excludeFolder url="file://$MODULE_DIR$/fiaranow_flutter/.fvm/versions/3.24.4/examples/layers/.dart_tool" />
      <excludeFolder url="file://$MODULE_DIR$/fiaranow_flutter/.fvm/versions/3.24.4/examples/layers/.pub" />
      <excludeFolder url="file://$MODULE_DIR$/fiaranow_flutter/.fvm/versions/3.24.4/examples/layers/build" />
      <excludeFolder url="file://$MODULE_DIR$/fiaranow_flutter/.fvm/versions/3.24.4/examples/platform_channel/.dart_tool" />
      <excludeFolder url="file://$MODULE_DIR$/fiaranow_flutter/.fvm/versions/3.24.4/examples/platform_channel/.pub" />
      <excludeFolder url="file://$MODULE_DIR$/fiaranow_flutter/.fvm/versions/3.24.4/examples/platform_channel/build" />
      <excludeFolder url="file://$MODULE_DIR$/fiaranow_flutter/.fvm/versions/3.24.4/examples/platform_channel_swift/.dart_tool" />
      <excludeFolder url="file://$MODULE_DIR$/fiaranow_flutter/.fvm/versions/3.24.4/examples/platform_channel_swift/.pub" />
      <excludeFolder url="file://$MODULE_DIR$/fiaranow_flutter/.fvm/versions/3.24.4/examples/platform_channel_swift/build" />
      <excludeFolder url="file://$MODULE_DIR$/fiaranow_flutter/.fvm/versions/3.24.4/examples/platform_view/.dart_tool" />
      <excludeFolder url="file://$MODULE_DIR$/fiaranow_flutter/.fvm/versions/3.24.4/examples/platform_view/.pub" />
      <excludeFolder url="file://$MODULE_DIR$/fiaranow_flutter/.fvm/versions/3.24.4/examples/platform_view/build" />
      <excludeFolder url="file://$MODULE_DIR$/fiaranow_flutter/.fvm/versions/3.24.4/examples/splash/.dart_tool" />
      <excludeFolder url="file://$MODULE_DIR$/fiaranow_flutter/.fvm/versions/3.24.4/examples/splash/.pub" />
      <excludeFolder url="file://$MODULE_DIR$/fiaranow_flutter/.fvm/versions/3.24.4/examples/splash/build" />
      <excludeFolder url="file://$MODULE_DIR$/fiaranow_flutter/.fvm/versions/3.24.4/examples/texture/.dart_tool" />
      <excludeFolder url="file://$MODULE_DIR$/fiaranow_flutter/.fvm/versions/3.24.4/examples/texture/.pub" />
      <excludeFolder url="file://$MODULE_DIR$/fiaranow_flutter/.fvm/versions/3.24.4/examples/texture/build" />
      <excludeFolder url="file://$MODULE_DIR$/fiaranow_flutter/.fvm/versions/3.24.4/packages/flutter/.dart_tool" />
      <excludeFolder url="file://$MODULE_DIR$/fiaranow_flutter/.fvm/versions/3.24.4/packages/flutter/.pub" />
      <excludeFolder url="file://$MODULE_DIR$/fiaranow_flutter/.fvm/versions/3.24.4/packages/flutter/build" />
      <excludeFolder url="file://$MODULE_DIR$/fiaranow_flutter/.fvm/versions/3.24.4/packages/flutter/test_private/.dart_tool" />
      <excludeFolder url="file://$MODULE_DIR$/fiaranow_flutter/.fvm/versions/3.24.4/packages/flutter/test_private/.pub" />
      <excludeFolder url="file://$MODULE_DIR$/fiaranow_flutter/.fvm/versions/3.24.4/packages/flutter/test_private/build" />
      <excludeFolder url="file://$MODULE_DIR$/fiaranow_flutter/.fvm/versions/3.24.4/packages/flutter/test_private/test/.dart_tool" />
      <excludeFolder url="file://$MODULE_DIR$/fiaranow_flutter/.fvm/versions/3.24.4/packages/flutter/test_private/test/.pub" />
      <excludeFolder url="file://$MODULE_DIR$/fiaranow_flutter/.fvm/versions/3.24.4/packages/flutter/test_private/test/build" />
      <excludeFolder url="file://$MODULE_DIR$/fiaranow_flutter/.fvm/versions/3.24.4/packages/flutter_driver/.dart_tool" />
      <excludeFolder url="file://$MODULE_DIR$/fiaranow_flutter/.fvm/versions/3.24.4/packages/flutter_driver/.pub" />
      <excludeFolder url="file://$MODULE_DIR$/fiaranow_flutter/.fvm/versions/3.24.4/packages/flutter_driver/build" />
      <excludeFolder url="file://$MODULE_DIR$/fiaranow_flutter/.fvm/versions/3.24.4/packages/flutter_goldens/.dart_tool" />
      <excludeFolder url="file://$MODULE_DIR$/fiaranow_flutter/.fvm/versions/3.24.4/packages/flutter_goldens/.pub" />
      <excludeFolder url="file://$MODULE_DIR$/fiaranow_flutter/.fvm/versions/3.24.4/packages/flutter_goldens/build" />
      <excludeFolder url="file://$MODULE_DIR$/fiaranow_flutter/.fvm/versions/3.24.4/packages/flutter_localizations/.dart_tool" />
      <excludeFolder url="file://$MODULE_DIR$/fiaranow_flutter/.fvm/versions/3.24.4/packages/flutter_localizations/.pub" />
      <excludeFolder url="file://$MODULE_DIR$/fiaranow_flutter/.fvm/versions/3.24.4/packages/flutter_localizations/build" />
      <excludeFolder url="file://$MODULE_DIR$/fiaranow_flutter/.fvm/versions/3.24.4/packages/flutter_test/.dart_tool" />
      <excludeFolder url="file://$MODULE_DIR$/fiaranow_flutter/.fvm/versions/3.24.4/packages/flutter_test/.pub" />
      <excludeFolder url="file://$MODULE_DIR$/fiaranow_flutter/.fvm/versions/3.24.4/packages/flutter_test/build" />
      <excludeFolder url="file://$MODULE_DIR$/fiaranow_flutter/.fvm/versions/3.24.4/packages/flutter_test/test/test_config/project_root/.dart_tool" />
      <excludeFolder url="file://$MODULE_DIR$/fiaranow_flutter/.fvm/versions/3.24.4/packages/flutter_test/test/test_config/project_root/.pub" />
      <excludeFolder url="file://$MODULE_DIR$/fiaranow_flutter/.fvm/versions/3.24.4/packages/flutter_test/test/test_config/project_root/build" />
      <excludeFolder url="file://$MODULE_DIR$/fiaranow_flutter/.fvm/versions/3.24.4/packages/flutter_tools/.dart_tool" />
      <excludeFolder url="file://$MODULE_DIR$/fiaranow_flutter/.fvm/versions/3.24.4/packages/flutter_tools/.pub" />
      <excludeFolder url="file://$MODULE_DIR$/fiaranow_flutter/.fvm/versions/3.24.4/packages/flutter_tools/build" />
      <excludeFolder url="file://$MODULE_DIR$/fiaranow_flutter/.fvm/versions/3.24.4/packages/flutter_web_plugins/.dart_tool" />
      <excludeFolder url="file://$MODULE_DIR$/fiaranow_flutter/.fvm/versions/3.24.4/packages/flutter_web_plugins/.pub" />
      <excludeFolder url="file://$MODULE_DIR$/fiaranow_flutter/.fvm/versions/3.24.4/packages/flutter_web_plugins/build" />
      <excludeFolder url="file://$MODULE_DIR$/fiaranow_flutter/.fvm/versions/3.24.4/packages/fuchsia_remote_debug_protocol/.dart_tool" />
      <excludeFolder url="file://$MODULE_DIR$/fiaranow_flutter/.fvm/versions/3.24.4/packages/fuchsia_remote_debug_protocol/.pub" />
      <excludeFolder url="file://$MODULE_DIR$/fiaranow_flutter/.fvm/versions/3.24.4/packages/fuchsia_remote_debug_protocol/build" />
      <excludeFolder url="file://$MODULE_DIR$/fiaranow_flutter/.fvm/versions/3.24.4/packages/integration_test/.dart_tool" />
      <excludeFolder url="file://$MODULE_DIR$/fiaranow_flutter/.fvm/versions/3.24.4/packages/integration_test/.pub" />
      <excludeFolder url="file://$MODULE_DIR$/fiaranow_flutter/.fvm/versions/3.24.4/packages/integration_test/build" />
      <excludeFolder url="file://$MODULE_DIR$/fiaranow_flutter/.fvm/versions/3.24.4/packages/integration_test/example/.dart_tool" />
      <excludeFolder url="file://$MODULE_DIR$/fiaranow_flutter/.fvm/versions/3.24.4/packages/integration_test/example/.pub" />
      <excludeFolder url="file://$MODULE_DIR$/fiaranow_flutter/.fvm/versions/3.24.4/packages/integration_test/example/build" />
      <excludeFolder url="file://$MODULE_DIR$/fiaranow_flutter/.fvm/versions/3.24.4/packages/integration_test/integration_test_macos/.dart_tool" />
      <excludeFolder url="file://$MODULE_DIR$/fiaranow_flutter/.fvm/versions/3.24.4/packages/integration_test/integration_test_macos/.pub" />
      <excludeFolder url="file://$MODULE_DIR$/fiaranow_flutter/.fvm/versions/3.24.4/packages/integration_test/integration_test_macos/build" />
      <excludeFolder url="file://$MODULE_DIR$/fiaranow_flutter/.pub" />
      <excludeFolder url="file://$MODULE_DIR$/fiaranow_flutter/build" />
      <excludeFolder url="file://$MODULE_DIR$/fiaranow_flutter/ios/.symlinks/plugins/cloud_firestore/.dart_tool" />
      <excludeFolder url="file://$MODULE_DIR$/fiaranow_flutter/ios/.symlinks/plugins/cloud_firestore/.pub" />
      <excludeFolder url="file://$MODULE_DIR$/fiaranow_flutter/ios/.symlinks/plugins/cloud_firestore/build" />
      <excludeFolder url="file://$MODULE_DIR$/fiaranow_flutter/ios/.symlinks/plugins/cloud_firestore/example/.dart_tool" />
      <excludeFolder url="file://$MODULE_DIR$/fiaranow_flutter/ios/.symlinks/plugins/cloud_firestore/example/.pub" />
      <excludeFolder url="file://$MODULE_DIR$/fiaranow_flutter/ios/.symlinks/plugins/cloud_firestore/example/build" />
      <excludeFolder url="file://$MODULE_DIR$/fiaranow_flutter/ios/.symlinks/plugins/cloud_functions/.dart_tool" />
      <excludeFolder url="file://$MODULE_DIR$/fiaranow_flutter/ios/.symlinks/plugins/cloud_functions/.pub" />
      <excludeFolder url="file://$MODULE_DIR$/fiaranow_flutter/ios/.symlinks/plugins/cloud_functions/build" />
      <excludeFolder url="file://$MODULE_DIR$/fiaranow_flutter/ios/.symlinks/plugins/cloud_functions/example/.dart_tool" />
      <excludeFolder url="file://$MODULE_DIR$/fiaranow_flutter/ios/.symlinks/plugins/cloud_functions/example/.pub" />
      <excludeFolder url="file://$MODULE_DIR$/fiaranow_flutter/ios/.symlinks/plugins/cloud_functions/example/build" />
      <excludeFolder url="file://$MODULE_DIR$/fiaranow_flutter/ios/.symlinks/plugins/desktop_webview_auth/.dart_tool" />
      <excludeFolder url="file://$MODULE_DIR$/fiaranow_flutter/ios/.symlinks/plugins/desktop_webview_auth/.pub" />
      <excludeFolder url="file://$MODULE_DIR$/fiaranow_flutter/ios/.symlinks/plugins/desktop_webview_auth/build" />
      <excludeFolder url="file://$MODULE_DIR$/fiaranow_flutter/ios/.symlinks/plugins/desktop_webview_auth/example/.dart_tool" />
      <excludeFolder url="file://$MODULE_DIR$/fiaranow_flutter/ios/.symlinks/plugins/desktop_webview_auth/example/.pub" />
      <excludeFolder url="file://$MODULE_DIR$/fiaranow_flutter/ios/.symlinks/plugins/desktop_webview_auth/example/build" />
      <excludeFolder url="file://$MODULE_DIR$/fiaranow_flutter/ios/.symlinks/plugins/device_info_plus/.dart_tool" />
      <excludeFolder url="file://$MODULE_DIR$/fiaranow_flutter/ios/.symlinks/plugins/device_info_plus/.pub" />
      <excludeFolder url="file://$MODULE_DIR$/fiaranow_flutter/ios/.symlinks/plugins/device_info_plus/build" />
      <excludeFolder url="file://$MODULE_DIR$/fiaranow_flutter/ios/.symlinks/plugins/device_info_plus/example/.dart_tool" />
      <excludeFolder url="file://$MODULE_DIR$/fiaranow_flutter/ios/.symlinks/plugins/device_info_plus/example/.pub" />
      <excludeFolder url="file://$MODULE_DIR$/fiaranow_flutter/ios/.symlinks/plugins/device_info_plus/example/build" />
      <excludeFolder url="file://$MODULE_DIR$/fiaranow_flutter/ios/.symlinks/plugins/file_picker/.dart_tool" />
      <excludeFolder url="file://$MODULE_DIR$/fiaranow_flutter/ios/.symlinks/plugins/file_picker/.pub" />
      <excludeFolder url="file://$MODULE_DIR$/fiaranow_flutter/ios/.symlinks/plugins/file_picker/build" />
      <excludeFolder url="file://$MODULE_DIR$/fiaranow_flutter/ios/.symlinks/plugins/file_picker/example/.dart_tool" />
      <excludeFolder url="file://$MODULE_DIR$/fiaranow_flutter/ios/.symlinks/plugins/file_picker/example/.pub" />
      <excludeFolder url="file://$MODULE_DIR$/fiaranow_flutter/ios/.symlinks/plugins/file_picker/example/build" />
      <excludeFolder url="file://$MODULE_DIR$/fiaranow_flutter/ios/.symlinks/plugins/firebase_analytics/.dart_tool" />
      <excludeFolder url="file://$MODULE_DIR$/fiaranow_flutter/ios/.symlinks/plugins/firebase_analytics/.pub" />
      <excludeFolder url="file://$MODULE_DIR$/fiaranow_flutter/ios/.symlinks/plugins/firebase_analytics/build" />
      <excludeFolder url="file://$MODULE_DIR$/fiaranow_flutter/ios/.symlinks/plugins/firebase_analytics/example/.dart_tool" />
      <excludeFolder url="file://$MODULE_DIR$/fiaranow_flutter/ios/.symlinks/plugins/firebase_analytics/example/.pub" />
      <excludeFolder url="file://$MODULE_DIR$/fiaranow_flutter/ios/.symlinks/plugins/firebase_analytics/example/build" />
      <excludeFolder url="file://$MODULE_DIR$/fiaranow_flutter/ios/.symlinks/plugins/firebase_app_check/.dart_tool" />
      <excludeFolder url="file://$MODULE_DIR$/fiaranow_flutter/ios/.symlinks/plugins/firebase_app_check/.pub" />
      <excludeFolder url="file://$MODULE_DIR$/fiaranow_flutter/ios/.symlinks/plugins/firebase_app_check/build" />
      <excludeFolder url="file://$MODULE_DIR$/fiaranow_flutter/ios/.symlinks/plugins/firebase_app_check/example/.dart_tool" />
      <excludeFolder url="file://$MODULE_DIR$/fiaranow_flutter/ios/.symlinks/plugins/firebase_app_check/example/.pub" />
      <excludeFolder url="file://$MODULE_DIR$/fiaranow_flutter/ios/.symlinks/plugins/firebase_app_check/example/build" />
      <excludeFolder url="file://$MODULE_DIR$/fiaranow_flutter/ios/.symlinks/plugins/firebase_auth/.dart_tool" />
      <excludeFolder url="file://$MODULE_DIR$/fiaranow_flutter/ios/.symlinks/plugins/firebase_auth/.pub" />
      <excludeFolder url="file://$MODULE_DIR$/fiaranow_flutter/ios/.symlinks/plugins/firebase_auth/build" />
      <excludeFolder url="file://$MODULE_DIR$/fiaranow_flutter/ios/.symlinks/plugins/firebase_auth/example/.dart_tool" />
      <excludeFolder url="file://$MODULE_DIR$/fiaranow_flutter/ios/.symlinks/plugins/firebase_auth/example/.pub" />
      <excludeFolder url="file://$MODULE_DIR$/fiaranow_flutter/ios/.symlinks/plugins/firebase_auth/example/build" />
      <excludeFolder url="file://$MODULE_DIR$/fiaranow_flutter/ios/.symlinks/plugins/firebase_core/.dart_tool" />
      <excludeFolder url="file://$MODULE_DIR$/fiaranow_flutter/ios/.symlinks/plugins/firebase_core/.pub" />
      <excludeFolder url="file://$MODULE_DIR$/fiaranow_flutter/ios/.symlinks/plugins/firebase_core/build" />
      <excludeFolder url="file://$MODULE_DIR$/fiaranow_flutter/ios/.symlinks/plugins/firebase_core/example/.dart_tool" />
      <excludeFolder url="file://$MODULE_DIR$/fiaranow_flutter/ios/.symlinks/plugins/firebase_core/example/.pub" />
      <excludeFolder url="file://$MODULE_DIR$/fiaranow_flutter/ios/.symlinks/plugins/firebase_core/example/build" />
      <excludeFolder url="file://$MODULE_DIR$/fiaranow_flutter/ios/.symlinks/plugins/firebase_crashlytics/.dart_tool" />
      <excludeFolder url="file://$MODULE_DIR$/fiaranow_flutter/ios/.symlinks/plugins/firebase_crashlytics/.pub" />
      <excludeFolder url="file://$MODULE_DIR$/fiaranow_flutter/ios/.symlinks/plugins/firebase_crashlytics/build" />
      <excludeFolder url="file://$MODULE_DIR$/fiaranow_flutter/ios/.symlinks/plugins/firebase_crashlytics/example/.dart_tool" />
      <excludeFolder url="file://$MODULE_DIR$/fiaranow_flutter/ios/.symlinks/plugins/firebase_crashlytics/example/.pub" />
      <excludeFolder url="file://$MODULE_DIR$/fiaranow_flutter/ios/.symlinks/plugins/firebase_crashlytics/example/build" />
      <excludeFolder url="file://$MODULE_DIR$/fiaranow_flutter/ios/.symlinks/plugins/firebase_database/.dart_tool" />
      <excludeFolder url="file://$MODULE_DIR$/fiaranow_flutter/ios/.symlinks/plugins/firebase_database/.pub" />
      <excludeFolder url="file://$MODULE_DIR$/fiaranow_flutter/ios/.symlinks/plugins/firebase_database/build" />
      <excludeFolder url="file://$MODULE_DIR$/fiaranow_flutter/ios/.symlinks/plugins/firebase_database/example/.dart_tool" />
      <excludeFolder url="file://$MODULE_DIR$/fiaranow_flutter/ios/.symlinks/plugins/firebase_database/example/.pub" />
      <excludeFolder url="file://$MODULE_DIR$/fiaranow_flutter/ios/.symlinks/plugins/firebase_database/example/build" />
      <excludeFolder url="file://$MODULE_DIR$/fiaranow_flutter/ios/.symlinks/plugins/firebase_dynamic_links/.dart_tool" />
      <excludeFolder url="file://$MODULE_DIR$/fiaranow_flutter/ios/.symlinks/plugins/firebase_dynamic_links/.pub" />
      <excludeFolder url="file://$MODULE_DIR$/fiaranow_flutter/ios/.symlinks/plugins/firebase_dynamic_links/build" />
      <excludeFolder url="file://$MODULE_DIR$/fiaranow_flutter/ios/.symlinks/plugins/firebase_dynamic_links/example/.dart_tool" />
      <excludeFolder url="file://$MODULE_DIR$/fiaranow_flutter/ios/.symlinks/plugins/firebase_dynamic_links/example/.pub" />
      <excludeFolder url="file://$MODULE_DIR$/fiaranow_flutter/ios/.symlinks/plugins/firebase_dynamic_links/example/build" />
      <excludeFolder url="file://$MODULE_DIR$/fiaranow_flutter/ios/.symlinks/plugins/firebase_messaging/.dart_tool" />
      <excludeFolder url="file://$MODULE_DIR$/fiaranow_flutter/ios/.symlinks/plugins/firebase_messaging/.pub" />
      <excludeFolder url="file://$MODULE_DIR$/fiaranow_flutter/ios/.symlinks/plugins/firebase_messaging/build" />
      <excludeFolder url="file://$MODULE_DIR$/fiaranow_flutter/ios/.symlinks/plugins/firebase_messaging/example/.dart_tool" />
      <excludeFolder url="file://$MODULE_DIR$/fiaranow_flutter/ios/.symlinks/plugins/firebase_messaging/example/.pub" />
      <excludeFolder url="file://$MODULE_DIR$/fiaranow_flutter/ios/.symlinks/plugins/firebase_messaging/example/build" />
      <excludeFolder url="file://$MODULE_DIR$/fiaranow_flutter/ios/.symlinks/plugins/firebase_storage/.dart_tool" />
      <excludeFolder url="file://$MODULE_DIR$/fiaranow_flutter/ios/.symlinks/plugins/firebase_storage/.pub" />
      <excludeFolder url="file://$MODULE_DIR$/fiaranow_flutter/ios/.symlinks/plugins/firebase_storage/build" />
      <excludeFolder url="file://$MODULE_DIR$/fiaranow_flutter/ios/.symlinks/plugins/firebase_storage/example/.dart_tool" />
      <excludeFolder url="file://$MODULE_DIR$/fiaranow_flutter/ios/.symlinks/plugins/firebase_storage/example/.pub" />
      <excludeFolder url="file://$MODULE_DIR$/fiaranow_flutter/ios/.symlinks/plugins/firebase_storage/example/build" />
      <excludeFolder url="file://$MODULE_DIR$/fiaranow_flutter/ios/.symlinks/plugins/flutter_native_splash/.dart_tool" />
      <excludeFolder url="file://$MODULE_DIR$/fiaranow_flutter/ios/.symlinks/plugins/flutter_native_splash/.pub" />
      <excludeFolder url="file://$MODULE_DIR$/fiaranow_flutter/ios/.symlinks/plugins/flutter_native_splash/build" />
      <excludeFolder url="file://$MODULE_DIR$/fiaranow_flutter/ios/.symlinks/plugins/flutter_native_splash/example/.dart_tool" />
      <excludeFolder url="file://$MODULE_DIR$/fiaranow_flutter/ios/.symlinks/plugins/flutter_native_splash/example/.pub" />
      <excludeFolder url="file://$MODULE_DIR$/fiaranow_flutter/ios/.symlinks/plugins/flutter_native_splash/example/build" />
      <excludeFolder url="file://$MODULE_DIR$/fiaranow_flutter/ios/.symlinks/plugins/geocoding_ios/.dart_tool" />
      <excludeFolder url="file://$MODULE_DIR$/fiaranow_flutter/ios/.symlinks/plugins/geocoding_ios/.pub" />
      <excludeFolder url="file://$MODULE_DIR$/fiaranow_flutter/ios/.symlinks/plugins/geocoding_ios/build" />
      <excludeFolder url="file://$MODULE_DIR$/fiaranow_flutter/ios/.symlinks/plugins/geocoding_ios/example/.dart_tool" />
      <excludeFolder url="file://$MODULE_DIR$/fiaranow_flutter/ios/.symlinks/plugins/geocoding_ios/example/.pub" />
      <excludeFolder url="file://$MODULE_DIR$/fiaranow_flutter/ios/.symlinks/plugins/geocoding_ios/example/build" />
      <excludeFolder url="file://$MODULE_DIR$/fiaranow_flutter/ios/.symlinks/plugins/geolocator_apple/.dart_tool" />
      <excludeFolder url="file://$MODULE_DIR$/fiaranow_flutter/ios/.symlinks/plugins/geolocator_apple/.pub" />
      <excludeFolder url="file://$MODULE_DIR$/fiaranow_flutter/ios/.symlinks/plugins/geolocator_apple/build" />
      <excludeFolder url="file://$MODULE_DIR$/fiaranow_flutter/ios/.symlinks/plugins/geolocator_apple/example/.dart_tool" />
      <excludeFolder url="file://$MODULE_DIR$/fiaranow_flutter/ios/.symlinks/plugins/geolocator_apple/example/.pub" />
      <excludeFolder url="file://$MODULE_DIR$/fiaranow_flutter/ios/.symlinks/plugins/geolocator_apple/example/build" />
      <excludeFolder url="file://$MODULE_DIR$/fiaranow_flutter/ios/.symlinks/plugins/google_sign_in_ios/.dart_tool" />
      <excludeFolder url="file://$MODULE_DIR$/fiaranow_flutter/ios/.symlinks/plugins/google_sign_in_ios/.pub" />
      <excludeFolder url="file://$MODULE_DIR$/fiaranow_flutter/ios/.symlinks/plugins/google_sign_in_ios/build" />
      <excludeFolder url="file://$MODULE_DIR$/fiaranow_flutter/ios/.symlinks/plugins/google_sign_in_ios/example/.dart_tool" />
      <excludeFolder url="file://$MODULE_DIR$/fiaranow_flutter/ios/.symlinks/plugins/google_sign_in_ios/example/.pub" />
      <excludeFolder url="file://$MODULE_DIR$/fiaranow_flutter/ios/.symlinks/plugins/google_sign_in_ios/example/build" />
      <excludeFolder url="file://$MODULE_DIR$/fiaranow_flutter/ios/.symlinks/plugins/image_picker_ios/.dart_tool" />
      <excludeFolder url="file://$MODULE_DIR$/fiaranow_flutter/ios/.symlinks/plugins/image_picker_ios/.pub" />
      <excludeFolder url="file://$MODULE_DIR$/fiaranow_flutter/ios/.symlinks/plugins/image_picker_ios/build" />
      <excludeFolder url="file://$MODULE_DIR$/fiaranow_flutter/ios/.symlinks/plugins/image_picker_ios/example/.dart_tool" />
      <excludeFolder url="file://$MODULE_DIR$/fiaranow_flutter/ios/.symlinks/plugins/image_picker_ios/example/.pub" />
      <excludeFolder url="file://$MODULE_DIR$/fiaranow_flutter/ios/.symlinks/plugins/image_picker_ios/example/build" />
      <excludeFolder url="file://$MODULE_DIR$/fiaranow_flutter/ios/.symlinks/plugins/in_app_review/.dart_tool" />
      <excludeFolder url="file://$MODULE_DIR$/fiaranow_flutter/ios/.symlinks/plugins/in_app_review/.pub" />
      <excludeFolder url="file://$MODULE_DIR$/fiaranow_flutter/ios/.symlinks/plugins/in_app_review/build" />
      <excludeFolder url="file://$MODULE_DIR$/fiaranow_flutter/ios/.symlinks/plugins/in_app_review/example/.dart_tool" />
      <excludeFolder url="file://$MODULE_DIR$/fiaranow_flutter/ios/.symlinks/plugins/in_app_review/example/.pub" />
      <excludeFolder url="file://$MODULE_DIR$/fiaranow_flutter/ios/.symlinks/plugins/in_app_review/example/build" />
      <excludeFolder url="file://$MODULE_DIR$/fiaranow_flutter/ios/.symlinks/plugins/package_info_plus/.dart_tool" />
      <excludeFolder url="file://$MODULE_DIR$/fiaranow_flutter/ios/.symlinks/plugins/package_info_plus/.pub" />
      <excludeFolder url="file://$MODULE_DIR$/fiaranow_flutter/ios/.symlinks/plugins/package_info_plus/build" />
      <excludeFolder url="file://$MODULE_DIR$/fiaranow_flutter/ios/.symlinks/plugins/package_info_plus/example/.dart_tool" />
      <excludeFolder url="file://$MODULE_DIR$/fiaranow_flutter/ios/.symlinks/plugins/package_info_plus/example/.pub" />
      <excludeFolder url="file://$MODULE_DIR$/fiaranow_flutter/ios/.symlinks/plugins/package_info_plus/example/build" />
      <excludeFolder url="file://$MODULE_DIR$/fiaranow_flutter/ios/.symlinks/plugins/restart/.dart_tool" />
      <excludeFolder url="file://$MODULE_DIR$/fiaranow_flutter/ios/.symlinks/plugins/restart/.pub" />
      <excludeFolder url="file://$MODULE_DIR$/fiaranow_flutter/ios/.symlinks/plugins/restart/build" />
      <excludeFolder url="file://$MODULE_DIR$/fiaranow_flutter/ios/.symlinks/plugins/restart/example/.dart_tool" />
      <excludeFolder url="file://$MODULE_DIR$/fiaranow_flutter/ios/.symlinks/plugins/restart/example/.pub" />
      <excludeFolder url="file://$MODULE_DIR$/fiaranow_flutter/ios/.symlinks/plugins/restart/example/build" />
      <excludeFolder url="file://$MODULE_DIR$/fiaranow_flutter/ios/.symlinks/plugins/sqflite_darwin/.dart_tool" />
      <excludeFolder url="file://$MODULE_DIR$/fiaranow_flutter/ios/.symlinks/plugins/sqflite_darwin/.pub" />
      <excludeFolder url="file://$MODULE_DIR$/fiaranow_flutter/ios/.symlinks/plugins/sqflite_darwin/build" />
      <excludeFolder url="file://$MODULE_DIR$/fiaranow_flutter/ios/.symlinks/plugins/sqflite_darwin/example/.dart_tool" />
      <excludeFolder url="file://$MODULE_DIR$/fiaranow_flutter/ios/.symlinks/plugins/sqflite_darwin/example/.pub" />
      <excludeFolder url="file://$MODULE_DIR$/fiaranow_flutter/ios/.symlinks/plugins/sqflite_darwin/example/build" />
      <excludeFolder url="file://$MODULE_DIR$/fiaranow_flutter/ios/.symlinks/plugins/url_launcher_ios/.dart_tool" />
      <excludeFolder url="file://$MODULE_DIR$/fiaranow_flutter/ios/.symlinks/plugins/url_launcher_ios/.pub" />
      <excludeFolder url="file://$MODULE_DIR$/fiaranow_flutter/ios/.symlinks/plugins/url_launcher_ios/build" />
      <excludeFolder url="file://$MODULE_DIR$/fiaranow_flutter/ios/.symlinks/plugins/url_launcher_ios/example/.dart_tool" />
      <excludeFolder url="file://$MODULE_DIR$/fiaranow_flutter/ios/.symlinks/plugins/url_launcher_ios/example/.pub" />
      <excludeFolder url="file://$MODULE_DIR$/fiaranow_flutter/ios/.symlinks/plugins/url_launcher_ios/example/build" />
      <excludeFolder url="file://$MODULE_DIR$/fiaranow_flutter/ios/.symlinks/plugins/wakelock_plus/.dart_tool" />
      <excludeFolder url="file://$MODULE_DIR$/fiaranow_flutter/ios/.symlinks/plugins/wakelock_plus/.pub" />
      <excludeFolder url="file://$MODULE_DIR$/fiaranow_flutter/ios/.symlinks/plugins/wakelock_plus/build" />
      <excludeFolder url="file://$MODULE_DIR$/fiaranow_flutter/ios/.symlinks/plugins/wakelock_plus/example/.dart_tool" />
      <excludeFolder url="file://$MODULE_DIR$/fiaranow_flutter/ios/.symlinks/plugins/wakelock_plus/example/.pub" />
      <excludeFolder url="file://$MODULE_DIR$/fiaranow_flutter/ios/.symlinks/plugins/wakelock_plus/example/build" />
      <excludeFolder url="file://$MODULE_DIR$/fiaranow_flutter/ios/.symlinks/plugins/workmanager/.dart_tool" />
      <excludeFolder url="file://$MODULE_DIR$/fiaranow_flutter/ios/.symlinks/plugins/workmanager/.pub" />
      <excludeFolder url="file://$MODULE_DIR$/fiaranow_flutter/ios/.symlinks/plugins/workmanager/build" />
    </content>
    <orderEntry type="inheritedJdk" />
    <orderEntry type="sourceFolder" forTests="false" />
  </component>
</module>