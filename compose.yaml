name: fiaranow-dev

services:
  # 🔥 Firebase Functions TypeScript Watch
  tsc-watch:
    image: node:22-alpine
    working_dir: /workspace/firebase/functions
    command: >
      sh -c "
        echo '📦 Installing dependencies...' &&
        npm install &&
        echo '🔥 Starting TypeScript watch...' &&
        npm run build:watch
      "
    volumes:
      - .:/workspace
      - firebase_functions_node_modules:/workspace/firebase/functions/node_modules
    environment:
      - NODE_ENV=${NODE_ENV:-development}
    restart: "no"
    networks:
      - fiaranow-dev

  # 🔥 Firebase Emulators
  firebase-emulators:
    image: node:22-alpine
    working_dir: /workspace/firebase
    ports:
      - "5001:5001"   # Firebase Functions
      - "8080:8080"   # Firebase Firestore
      - "9099:9099"   # Firebase Auth
      - "9000:9000"   # Firebase UI
      - "5025:5025"   # Firebase Hosting
      - "8085:8085"   # Firebase PubSub
      - "9199:9199"   # Firebase Storage
      - "9229:9229"   # Firebase Debug
    volumes:
      - .:/workspace
      - firebase_functions_node_modules:/workspace/firebase/functions/node_modules
      - ~/.config/firebase:/root/.config/firebase:ro
      - ~/.config/gcloud:/root/.config/gcloud:ro
    environment:
      - NODE_ENV=${NODE_ENV:-development}
      - USE_PRODUCTION_DATA=${USE_PRODUCTION_DATA:-false}
      - FIREBASE_EMULATOR_HUB=localhost:4400
    command: >
      sh -c "
        echo '📦 Installing system dependencies...' &&
        apk add --no-cache openjdk11-jre-headless &&
        echo '📦 Installing Node.js dependencies...' &&
        cd functions && npm install && cd .. &&
        echo '🔥 Installing Firebase CLI globally...' &&
        npm install -g firebase-tools &&
        echo '🔥 Starting Firebase Emulators...' &&
        sleep 3 &&
        if [ \"$${USE_PRODUCTION_DATA}\" = \"true\" ]; then
          echo '📦 Starting with PRODUCTION DATA (read-only mode)...' &&
          firebase emulators:start --only functions,firestore,auth,pubsub,storage --inspect-functions 9229 --import /workspace/firebase/emulators_data
        else
          echo '🛠️ Starting with local development data...' &&
          firebase emulators:start --only functions,firestore,auth,pubsub,storage --inspect-functions 9229 --import /workspace/firebase/functions/emulators_data --export-on-exit
        fi
      "
    depends_on:
      - tsc-watch
    restart: "no"
    networks:
      - fiaranow-dev

  # 🌐 Admin SvelteKit Development Server
  admin-sveltekit:
    image: node:22-alpine
    user: "1000:1000"
    working_dir: /workspace/admin_sveltekit
    ports:
      - "5173:5173"
    volumes:
      - .:/workspace
      - admin_sveltekit_node_modules:/workspace/admin_sveltekit/node_modules
    environment:
      - NODE_ENV=${NODE_ENV:-development}
      - VITE_HOST=${VITE_HOST:-0.0.0.0}
    command: >
      sh -c "
        echo '📦 Installing system dependencies...' &&
        apk add --no-cache netcat-openbsd &&
        echo '📦 Installing Node.js dependencies...' &&
        npm install &&
        echo '⏰ Waiting for Firebase emulators to be ready...' &&
        until nc -z firebase-emulators 8080 2>/dev/null; do
          echo '⏰ Waiting for Firestore emulator...'
          sleep 2
        done &&
        until nc -z firebase-emulators 5001 2>/dev/null; do
          echo '⏰ Waiting for Functions emulator...'
          sleep 2
        done &&
        echo '🌐 Starting SvelteKit dev server...' &&
        npm run dev -- --host 0.0.0.0 --port 5173
      "
    depends_on:
      - firebase-emulators
    restart: "no"
    networks:
      - fiaranow-dev

  # ⏰ Scheduled Functions Simulator (Emulator Only)
  scheduled-functions-simulator:
    image: node:22-alpine
    working_dir: /workspace/firebase/emulator-tools/scheduled-functions-simulation
    volumes:
      - .:/workspace
    environment:
      - NODE_ENV=development
      - GOOGLE_CLOUD_PROJECT=fiaranow
      - FUNCTIONS_EMULATOR=true
      - FIRESTORE_EMULATOR_HOST=firebase-emulators:8080
      - FIREBASE_AUTH_EMULATOR_HOST=firebase-emulators:9099
      - CALCULATE_TRIP_COSTS_INTERVAL=60000     # 1 minute
      - NOTIFY_EXPIRING_DOCS_INTERVAL=300000    # 5 minutes  
      - GENERATE_FEEDBACK_STATS_INTERVAL=300000 # 5 minutes
      - CLEANUP_ARCHIVED_CHATS_INTERVAL=300000  # 5 minutes
      - CHECK_DRIVER_TIMEOUTS_INTERVAL=60000    # 1 minute
    command: >
      sh -c "
        echo '📦 Installing dependencies...' &&
        npm install &&
        echo '⏰ Starting scheduled functions simulator (emulator mode)...' &&
        node run-scheduled-functions.js
      "
    depends_on:
      - firebase-emulators
      - tsc-watch
    restart: "no"
    networks:
      - fiaranow-dev




volumes:
  firebase_functions_node_modules:
    driver: local
  admin_sveltekit_node_modules:
    driver: local

networks:
  fiaranow-dev:
    driver: bridge
    name: fiaranow-dev-network 