import Flutter
import UIKit
import GoogleMaps
import GoogleSignIn
import Firebase
import FirebaseMessaging
import UserNotifications
import restart

@main
@objc class AppDelegate: FlutterAppDelegate {
  override func application(
    _ application: UIApplication,
    didFinishLaunchingWithOptions launchOptions: [UIApplication.LaunchOptionsKey: Any]?
  ) -> Bool {
    // Restart plugin registration
    RestartPlugin.generatedPluginRegistrantRegisterCallback = { [weak self] in
      GeneratedPluginRegistrant.register(with: self!)
    }
    
    // Initialize Google Maps
    GMSServices.provideAPIKey("AIzaSyCYv7VWSjBh8_XziLq9QqwrjEygnA9uNmE")
    
    // Configure Firebase
    FirebaseApp.configure()
    
    // Set up notifications - configure delegate and messaging
    UNUserNotificationCenter.current().delegate = self
    Messaging.messaging().delegate = self
    
    // Request notification permissions first, then register for remote notifications
    UNUserNotificationCenter.current().requestAuthorization(options: [.alert, .badge, .sound]) { granted, error in
      DispatchQueue.main.async {
        if granted {
          application.registerForRemoteNotifications()
        } else if let error = error {
          print("Notification permission error: \(error)")
        }
      }
    }
    
    GeneratedPluginRegistrant.register(with: self)
    return super.application(application, didFinishLaunchingWithOptions: launchOptions)
  }
  
  override func application(_ app: UIApplication,
                          open url: URL,
                          options: [UIApplication.OpenURLOptionsKey: Any] = [:]) -> Bool {
    return GIDSignIn.sharedInstance.handle(url)
  }
  
  override func application(_ application: UIApplication,
                          didRegisterForRemoteNotificationsWithDeviceToken deviceToken: Data) {
    Messaging.messaging().apnsToken = deviceToken
    super.application(application, didRegisterForRemoteNotificationsWithDeviceToken: deviceToken)
  }
  
  // Handle notification presentation when app is in foreground
  override func userNotificationCenter(_ center: UNUserNotificationCenter,
                                willPresent notification: UNNotification,
                                withCompletionHandler completionHandler: @escaping (UNNotificationPresentationOptions) -> Void) {
    let userInfo = notification.request.content.userInfo
    
    // Print message ID for debugging
    if let messageID = userInfo["gcm.message_id"] as? String {
      print("Message ID: \(messageID)")
    }
    
    // Show notification when app is in foreground
    completionHandler([[.banner, .sound, .badge]])
  }
  
  // Handle user tapping on notification
  override func userNotificationCenter(_ center: UNUserNotificationCenter,
                                didReceive response: UNNotificationResponse,
                                withCompletionHandler completionHandler: @escaping () -> Void) {
    let userInfo = response.notification.request.content.userInfo
    
    // Print message ID for debugging
    if let messageID = userInfo["gcm.message_id"] as? String {
      print("Message ID from user tapping notification: \(messageID)")
    }
    
    completionHandler()
  }
}

extension AppDelegate: MessagingDelegate {
  func messaging(_ messaging: Messaging, didReceiveRegistrationToken fcmToken: String?) {
    debugPrint("Firebase registration token: \(String(describing: fcmToken))")
    
    // Send token to application server if needed
    let dataDict: [String: String] = ["token": fcmToken ?? ""]
    NotificationCenter.default.post(
      name: Notification.Name("FCMToken"),
      object: nil,
      userInfo: dataDict
    )
  }
}
