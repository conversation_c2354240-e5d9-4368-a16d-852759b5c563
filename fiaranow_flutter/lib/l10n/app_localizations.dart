import 'dart:async';

import 'package:flutter/foundation.dart';
import 'package:flutter/widgets.dart';
import 'package:flutter_localizations/flutter_localizations.dart';
import 'package:intl/intl.dart' as intl;

import 'app_localizations_en.dart';
import 'app_localizations_fr.dart';

// ignore_for_file: type=lint

/// Callers can lookup localized strings with an instance of AppLocalizations
/// returned by `AppLocalizations.of(context)`.
///
/// Applications need to include `AppLocalizations.delegate()` in their app's
/// `localizationDelegates` list, and the locales they support in the app's
/// `supportedLocales` list. For example:
///
/// ```dart
/// import 'l10n/app_localizations.dart';
///
/// return MaterialApp(
///   localizationsDelegates: AppLocalizations.localizationsDelegates,
///   supportedLocales: AppLocalizations.supportedLocales,
///   home: MyApplicationHome(),
/// );
/// ```
///
/// ## Update pubspec.yaml
///
/// Please make sure to update your pubspec.yaml to include the following
/// packages:
///
/// ```yaml
/// dependencies:
///   # Internationalization support.
///   flutter_localizations:
///     sdk: flutter
///   intl: any # Use the pinned version from flutter_localizations
///
///   # Rest of dependencies
/// ```
///
/// ## iOS Applications
///
/// iOS applications define key application metadata, including supported
/// locales, in an Info.plist file that is built into the application bundle.
/// To configure the locales supported by your app, you’ll need to edit this
/// file.
///
/// First, open your project’s ios/Runner.xcworkspace Xcode workspace file.
/// Then, in the Project Navigator, open the Info.plist file under the Runner
/// project’s Runner folder.
///
/// Next, select the Information Property List item, select Add Item from the
/// Editor menu, then select Localizations from the pop-up menu.
///
/// Select and expand the newly-created Localizations item then, for each
/// locale your application supports, add a new item and select the locale
/// you wish to add from the pop-up menu in the Value field. This list should
/// be consistent with the languages listed in the AppLocalizations.supportedLocales
/// property.
abstract class AppLocalizations {
  AppLocalizations(String locale) : localeName = intl.Intl.canonicalizedLocale(locale.toString());

  final String localeName;

  static AppLocalizations? of(BuildContext context) {
    return Localizations.of<AppLocalizations>(context, AppLocalizations);
  }

  static const LocalizationsDelegate<AppLocalizations> delegate = _AppLocalizationsDelegate();

  /// A list of this localizations delegate along with the default localizations
  /// delegates.
  ///
  /// Returns a list of localizations delegates containing this delegate along with
  /// GlobalMaterialLocalizations.delegate, GlobalCupertinoLocalizations.delegate,
  /// and GlobalWidgetsLocalizations.delegate.
  ///
  /// Additional delegates can be added by appending to this list in
  /// MaterialApp. This list does not have to be used at all if a custom list
  /// of delegates is preferred or required.
  static const List<LocalizationsDelegate<dynamic>> localizationsDelegates = <LocalizationsDelegate<dynamic>>[
    delegate,
    GlobalMaterialLocalizations.delegate,
    GlobalCupertinoLocalizations.delegate,
    GlobalWidgetsLocalizations.delegate,
  ];

  /// A list of this localizations delegate's supported locales.
  static const List<Locale> supportedLocales = <Locale>[
    Locale('en'),
    Locale('fr')
  ];

  /// The title of the application
  ///
  /// In en, this message translates to:
  /// **'Fiaranow'**
  String get mainPage_appTitle;

  /// Title for the screen where the user chooses their primary activity
  ///
  /// In en, this message translates to:
  /// **'Choose Your Primary Activity'**
  String get mainPage_choosePrimaryActivity;

  /// Button text for selecting rider as primary activity
  ///
  /// In en, this message translates to:
  /// **'Rider'**
  String get mainPage_rider;

  /// Button text for selecting driver as primary activity
  ///
  /// In en, this message translates to:
  /// **'Driver'**
  String get mainPage_driver;

  /// Text indicating that the user can change their primary activity later
  ///
  /// In en, this message translates to:
  /// **'You can change this at any time in the settings menu, so no need to worry.'**
  String get mainPage_changeAnytime;

  /// Text for the menu header in the drawer
  ///
  /// In en, this message translates to:
  /// **'Menu'**
  String get menuDrawer_menu;

  /// Text for the logout option in the drawer
  ///
  /// In en, this message translates to:
  /// **'Logout'**
  String get menuDrawer_logout;

  /// Confirmation message for logout dialog
  ///
  /// In en, this message translates to:
  /// **'Are you sure you want to logout?'**
  String get dialog_logoutConfirmation;

  /// Text for the 'No' button in the logout confirmation dialog
  ///
  /// In en, this message translates to:
  /// **'No'**
  String get dialog_no;

  /// Text for the 'Yes' button in the logout confirmation dialog
  ///
  /// In en, this message translates to:
  /// **'Yes'**
  String get dialog_yes;

  /// Text for the confirm button in passenger count selection dialog
  ///
  /// In en, this message translates to:
  /// **'Confirm'**
  String get passengerCountDialog_confirm;

  /// Text for the choose language button
  ///
  /// In en, this message translates to:
  /// **'Choose Language'**
  String get mainPage_chooseLanguage;

  /// Label for theme selection in menu drawer
  ///
  /// In en, this message translates to:
  /// **'Theme'**
  String get menuDrawer_theme;

  /// Text for system theme option
  ///
  /// In en, this message translates to:
  /// **'System'**
  String get menuDrawer_themeSystem;

  /// Text for light theme option
  ///
  /// In en, this message translates to:
  /// **'Light'**
  String get menuDrawer_themeLight;

  /// Text for dark theme option
  ///
  /// In en, this message translates to:
  /// **'Dark'**
  String get menuDrawer_themeDark;

  /// Text for the English language option
  ///
  /// In en, this message translates to:
  /// **'English'**
  String get mainPage_english;

  /// Text for the French language option
  ///
  /// In en, this message translates to:
  /// **'French'**
  String get mainPage_french;

  /// Text for the welcome message
  ///
  /// In en, this message translates to:
  /// **'Welcome'**
  String get mainPage_welcome;

  /// Title for the screen where the user enables notifications
  ///
  /// In en, this message translates to:
  /// **'Enable Notifications'**
  String get notification_screenTitle;

  /// Button text for enabling notifications
  ///
  /// In en, this message translates to:
  /// **'Enable Notifications'**
  String get notification_enableButton;

  /// Description text explaining why notifications are needed for drivers
  ///
  /// In en, this message translates to:
  /// **'As a driver, you need to enable notifications to receive ride requests and important updates.'**
  String get notification_description;

  /// Title for the dialog indicating that notifications are required
  ///
  /// In en, this message translates to:
  /// **'Notifications Required'**
  String get notification_requiredTitle;

  /// Content text for the dialog indicating that notifications are required
  ///
  /// In en, this message translates to:
  /// **'Notifications are required for driver mode. Please enable them in settings.'**
  String get notification_requiredContent;

  /// Text for the cancel button in the dialog
  ///
  /// In en, this message translates to:
  /// **'Cancel'**
  String get notification_cancel;

  /// Text for the button to open app settings
  ///
  /// In en, this message translates to:
  /// **'Open Settings'**
  String get notification_openSettings;

  /// Title for the driver mode permission dialog
  ///
  /// In en, this message translates to:
  /// **'Driver Mode Permissions'**
  String get driverMode_permissionTitle;

  /// Description explaining why driver mode needs permissions
  ///
  /// In en, this message translates to:
  /// **'To operate as a driver, we need your permission to send notifications and track your location in the background.'**
  String get driverMode_permissionDescription;

  /// Label for notification permission in driver mode
  ///
  /// In en, this message translates to:
  /// **'Notifications for ride requests'**
  String get driverMode_notificationPermission;

  /// Label for background location permission in driver mode
  ///
  /// In en, this message translates to:
  /// **'Background location (Always Allow)'**
  String get driverMode_backgroundLocationPermission;

  /// Button text to grant all driver permissions
  ///
  /// In en, this message translates to:
  /// **'Grant Permissions'**
  String get driverMode_grantPermissions;

  /// Title for location permission required dialog
  ///
  /// In en, this message translates to:
  /// **'Location Permission Required'**
  String get driverMode_locationRequiredTitle;

  /// Content for location permission required dialog
  ///
  /// In en, this message translates to:
  /// **'Location permission is required for driver mode. Please enable it in settings.'**
  String get driverMode_locationRequiredContent;

  /// Title for background location instructions dialog
  ///
  /// In en, this message translates to:
  /// **'Background Location Required'**
  String get driverMode_backgroundLocationTitle;

  /// Explanation for why background location is needed
  ///
  /// In en, this message translates to:
  /// **'Driver mode requires \'Always Allow\' location permission to track your location and assign rides even when the app is in the background.'**
  String get driverMode_backgroundLocationExplanation;

  /// Instructions header for setting background location
  ///
  /// In en, this message translates to:
  /// **'Please follow these steps:'**
  String get driverMode_backgroundLocationInstructions;

  /// iOS step 1 for background location
  ///
  /// In en, this message translates to:
  /// **'Tap \'Open Settings\' below'**
  String get driverMode_iosStep1;

  /// iOS step 2 for background location
  ///
  /// In en, this message translates to:
  /// **'Select \'Location\' and choose \'Always\''**
  String get driverMode_iosStep2;

  /// iOS step 3 for background location
  ///
  /// In en, this message translates to:
  /// **'Return to the app and try again'**
  String get driverMode_iosStep3;

  /// Android step 1 for background location
  ///
  /// In en, this message translates to:
  /// **'Tap \'Open Settings\' below'**
  String get driverMode_androidStep1;

  /// Android step 2 for background location
  ///
  /// In en, this message translates to:
  /// **'Select \'Location\' and choose \'Allow all the time\''**
  String get driverMode_androidStep2;

  /// Android step 3 for background location
  ///
  /// In en, this message translates to:
  /// **'Return to the app and try again'**
  String get driverMode_androidStep3;

  /// Generic error message
  ///
  /// In en, this message translates to:
  /// **'An error occurred. Please try again.'**
  String get genericError;

  /// Title for the driver profile form screen
  ///
  /// In en, this message translates to:
  /// **'Driver Profile'**
  String get driverProfileForm_screenTitle;

  /// Label for the vehicle brand/make field
  ///
  /// In en, this message translates to:
  /// **'Vehicle Brand/Make'**
  String get driverProfileForm_vehicleBrand;

  /// Label for the vehicle model field
  ///
  /// In en, this message translates to:
  /// **'Vehicle Model'**
  String get driverProfileForm_vehicleModel;

  /// Label for the vehicle color field
  ///
  /// In en, this message translates to:
  /// **'Main Color'**
  String get driverProfileForm_vehicleColor;

  /// Label for the vehicle year field
  ///
  /// In en, this message translates to:
  /// **'Year'**
  String get driverProfileForm_vehicleYear;

  /// Label for the registration number field
  ///
  /// In en, this message translates to:
  /// **'Registration Number'**
  String get driverProfileForm_registrationNumber;

  /// Text for the save button
  ///
  /// In en, this message translates to:
  /// **'Save'**
  String get driverProfileForm_saveButton;

  /// Label for the maximum passengers field
  ///
  /// In en, this message translates to:
  /// **'Maximum Passengers'**
  String get driverProfileForm_maxPassengers;

  /// Text for the edit driver profile option in the drawer
  ///
  /// In en, this message translates to:
  /// **'Edit Driver Profile'**
  String get mainPage_editDriverProfile;

  /// Title for the snackbar when the user is not a driver
  ///
  /// In en, this message translates to:
  /// **'Not a Driver'**
  String get mainPage_notADriver;

  /// Message for the snackbar when the user is not a driver
  ///
  /// In en, this message translates to:
  /// **'You need to set your primary activity to Driver first.'**
  String get mainPage_setPrimaryActivityToDriver;

  /// Label for the profile tab
  ///
  /// In en, this message translates to:
  /// **'Profile'**
  String get mainPage_profile;

  /// Label for the maps tab
  ///
  /// In en, this message translates to:
  /// **'Map'**
  String get mainPage_maps;

  /// Label for the history tab
  ///
  /// In en, this message translates to:
  /// **'History'**
  String get mainPage_history;

  /// Label for the home tab
  ///
  /// In en, this message translates to:
  /// **'Home'**
  String get mainPage_home;

  /// Text for the primary activity option in the drawer
  ///
  /// In en, this message translates to:
  /// **'Primary Activity'**
  String get mainPage_primaryActivity;

  /// Text for the driver profile option in the drawer
  ///
  /// In en, this message translates to:
  /// **'Driver Profile'**
  String get mainPage_driverProfile;

  /// Hint text for the pick-up address input field
  ///
  /// In en, this message translates to:
  /// **'Pick-up address'**
  String get mapScreen_pickupAddressInputText;

  /// Hint text for the destination address input field
  ///
  /// In en, this message translates to:
  /// **'Destination address'**
  String get mapScreen_destinationAddressInputText;

  /// Text for the button to confirm pick-up location
  ///
  /// In en, this message translates to:
  /// **'Confirm Pick-up location?'**
  String get mapScreen_confirmPickupLocationButton;

  /// Text for the button to confirm destination
  ///
  /// In en, this message translates to:
  /// **'Confirm destination?'**
  String get mapScreen_confirmDestinationButton;

  /// Text indicating no drivers are available nearby
  ///
  /// In en, this message translates to:
  /// **'No drivers available nearby'**
  String get mapScreen_noDriversAvailable;

  /// Header text for the available drivers list
  ///
  /// In en, this message translates to:
  /// **'Available Drivers'**
  String get mapScreen_availableDriversHeader;

  /// Text for the button to select a driver
  ///
  /// In en, this message translates to:
  /// **'Select'**
  String get mapScreen_selectDriverButton;

  /// Text shown when driver doesn't have enough seats for passengers
  ///
  /// In en, this message translates to:
  /// **'Not enough seats'**
  String get mapScreen_notEnoughSeats;

  /// Text showing number of seats available
  ///
  /// In en, this message translates to:
  /// **'Seats: {maxPassengers}'**
  String mapScreen_seatsAvailable(int maxPassengers);

  /// Text showing number of seats needed
  ///
  /// In en, this message translates to:
  /// **'(Need {passengerCount})'**
  String mapScreen_seatsNeeded(int passengerCount);

  /// Text for the button to change addresses
  ///
  /// In en, this message translates to:
  /// **'Change address(es)'**
  String get mapScreen_changeAddressesButton;

  /// Text for the button to show nearby drivers
  ///
  /// In en, this message translates to:
  /// **'Show Nearby Drivers'**
  String get mapScreen_showNearbyDriversButton;

  /// Text for the button to stop the service
  ///
  /// In en, this message translates to:
  /// **'Stop Service'**
  String get mapScreen_stopServiceButton;

  /// Text for the button to start the service
  ///
  /// In en, this message translates to:
  /// **'Start Service'**
  String get mapScreen_startServiceButton;

  /// Text for the button to check the internet connection
  ///
  /// In en, this message translates to:
  /// **'Check your Internet'**
  String get mapScreen_checkInternetButton;

  /// Welcome text on the home screen
  ///
  /// In en, this message translates to:
  /// **'Welcome to Fiaranow'**
  String get home_welcomeText;

  /// Title for the phone number form screen
  ///
  /// In en, this message translates to:
  /// **'Set Phone Number'**
  String get phoneNumberForm_screenTitle;

  /// Label for the phone number field
  ///
  /// In en, this message translates to:
  /// **'Phone Number'**
  String get phoneNumberForm_phoneNumber;

  /// Text for the save button
  ///
  /// In en, this message translates to:
  /// **'Save'**
  String get phoneNumberForm_saveButton;

  /// Text indicating that the driver profile needs confirmation
  ///
  /// In en, this message translates to:
  /// **'Driver profile needs confirmation'**
  String get mapScreen_driverProfileNeedsConfirmation;

  /// Text indicating the trip distance
  ///
  /// In en, this message translates to:
  /// **'Trip Distance: {distance} km'**
  String mapScreen_tripDistance(String distance);

  /// Text indicating the estimated duration of the trip
  ///
  /// In en, this message translates to:
  /// **'Estimated Duration: {duration}'**
  String mapScreen_estimatedDuration(String duration);

  /// Text indicating no drivers are available nearby
  ///
  /// In en, this message translates to:
  /// **'No drivers available nearby'**
  String get mapScreen_noDriversAvailableNearby;

  /// Text indicating the road distance
  ///
  /// In en, this message translates to:
  /// **'Road Distance: {distance} km'**
  String mapScreen_roadDistance(String distance);

  /// Text indicating the approaching ETA
  ///
  /// In en, this message translates to:
  /// **'Approaching ETA: {eta} minutes'**
  String mapScreen_approachingETA(String eta);

  /// Text for the cancel button
  ///
  /// In en, this message translates to:
  /// **'Cancel'**
  String get mapScreen_cancel;

  /// Text for the trip requests header
  ///
  /// In en, this message translates to:
  /// **'Trip Requests'**
  String get mapScreen_tripRequests;

  /// Text indicating no trip requests
  ///
  /// In en, this message translates to:
  /// **'No trip requests at this time.'**
  String get mapScreen_noTripRequests;

  /// Text indicating the starting point
  ///
  /// In en, this message translates to:
  /// **'From: {startLat}, {startLon}'**
  String mapScreen_from(String startLat, String startLon);

  /// Text indicating the destination
  ///
  /// In en, this message translates to:
  /// **'To: {destLat}, {destLon}'**
  String mapScreen_to(String destLat, String destLon);

  /// Text indicating the trip distance
  ///
  /// In en, this message translates to:
  /// **'Trip distance: {distance} km, ETA: {eta} minutes'**
  String mapScreen_tripDistanceLabel(String distance, String eta);

  /// Text indicating the approaching distance
  ///
  /// In en, this message translates to:
  /// **'Approaching distance: {distance} km, ETA: {eta} minutes'**
  String mapScreen_approachingDistance(String distance, String eta);

  /// Text for the accept button
  ///
  /// In en, this message translates to:
  /// **'Accept'**
  String get mapScreen_accept;

  /// Text for the reject button
  ///
  /// In en, this message translates to:
  /// **'Reject'**
  String get mapScreen_reject;

  /// Text for confirming rejection
  ///
  /// In en, this message translates to:
  /// **'Confirm Rejection'**
  String get mapScreen_confirmRejection;

  /// Message for confirming rejection
  ///
  /// In en, this message translates to:
  /// **'Are you sure you want to reject this trip request?'**
  String get mapScreen_confirmRejectionMessage;

  /// Text indicating no active trip
  ///
  /// In en, this message translates to:
  /// **'No active trip.'**
  String get mapScreen_noActiveTrip;

  /// Text for the trip control header
  ///
  /// In en, this message translates to:
  /// **'Trip Control'**
  String get mapScreen_tripControl;

  /// Simple trip header text for viewing mode
  ///
  /// In en, this message translates to:
  /// **'Trip'**
  String get mapScreen_tripHeader;

  /// Text indicating the driver should approach the client
  ///
  /// In en, this message translates to:
  /// **'You should now be driving to approach the client...'**
  String get mapScreen_youShouldNowBeDriving;

  /// Text for the button indicating the driver has arrived
  ///
  /// In en, this message translates to:
  /// **'I have arrived (Awaiting Passenger)'**
  String get mapScreen_iHaveArrived;

  /// Text indicating the trip is waiting to start
  ///
  /// In en, this message translates to:
  /// **'Waiting for both to start the trip...'**
  String get mapScreen_waitingForBothToStart;

  /// Text indicating to wait for the passenger to start the trip
  ///
  /// In en, this message translates to:
  /// **'Please wait for the passenger to start the trip as well.'**
  String get mapScreen_pleaseWaitForPassenger;

  /// Text indicating the trip is in progress
  ///
  /// In en, this message translates to:
  /// **'Trip is in progress.'**
  String get mapScreen_tripInProgress;

  /// Text for the complete trip button
  ///
  /// In en, this message translates to:
  /// **'Complete Trip'**
  String get mapScreen_completeTrip;

  /// Text indicating the trip is completed
  ///
  /// In en, this message translates to:
  /// **'Trip is completed.'**
  String get mapScreen_tripCompleted;

  /// Text for the mark as paid button
  ///
  /// In en, this message translates to:
  /// **'Mark as Paid'**
  String get mapScreen_markAsPaid;

  /// Text indicating to wait for the driver to start the trip
  ///
  /// In en, this message translates to:
  /// **'Please wait for the driver to start the trip as well.'**
  String get mapScreen_pleaseWaitForDriver;

  /// Text for the button to change addresses
  ///
  /// In en, this message translates to:
  /// **'Change address(es)'**
  String get mapScreen_changeAddresses;

  /// Text for the button to show nearby drivers
  ///
  /// In en, this message translates to:
  /// **'Show Nearby Drivers'**
  String get mapScreen_showNearbyDrivers;

  /// Text for the choose primary activity option
  ///
  /// In en, this message translates to:
  /// **'Choose your primary activity'**
  String get home_choosePrimaryActivity;

  /// Text for the set phone number option
  ///
  /// In en, this message translates to:
  /// **'Set your phone number'**
  String get home_setPhoneNumber;

  /// Text for the allow push notifications option
  ///
  /// In en, this message translates to:
  /// **'Allow push notifications'**
  String get home_allowPushNotifications;

  /// Text for the allow location permission option
  ///
  /// In en, this message translates to:
  /// **'Allow location permission'**
  String get home_allowLocationPermission;

  /// Text for the get a ride now button
  ///
  /// In en, this message translates to:
  /// **'Get a ride Now'**
  String get home_getRideNow;

  /// Text for the reserve a ride button
  ///
  /// In en, this message translates to:
  /// **'Reserve a ride'**
  String get home_reserveRide;

  /// Text for the special offer header
  ///
  /// In en, this message translates to:
  /// **'Special offer'**
  String get home_specialOffer;

  /// Text for the reserve car no gas offer
  ///
  /// In en, this message translates to:
  /// **'• Reserve a car for the whole day for €25 (no gas)'**
  String get home_reserveCarNoGas;

  /// Text for the reserve car with gas offer
  ///
  /// In en, this message translates to:
  /// **'• Reserve for the whole day for €75 (including gas)'**
  String get home_reserveCarWithGas;

  /// Title for the trip details screen
  ///
  /// In en, this message translates to:
  /// **'Trip Details'**
  String get tripDetails_title;

  /// Label for the pickup time
  ///
  /// In en, this message translates to:
  /// **'Pickup Time'**
  String get tripDetails_pickupTime;

  /// Label for the distance
  ///
  /// In en, this message translates to:
  /// **'Distance'**
  String get tripDetails_distance;

  /// Label for the duration
  ///
  /// In en, this message translates to:
  /// **'Duration'**
  String get tripDetails_duration;

  /// Label for the cost
  ///
  /// In en, this message translates to:
  /// **'Cost'**
  String get tripDetails_cost;

  /// Label for the passenger
  ///
  /// In en, this message translates to:
  /// **'Passenger'**
  String get tripDetails_passenger;

  /// Label for the number of passengers in the trip
  ///
  /// In en, this message translates to:
  /// **'Passenger Count'**
  String get tripDetails_passengerCount;

  /// Label for the status
  ///
  /// In en, this message translates to:
  /// **'Status'**
  String get tripDetails_status;

  /// Label for the created at
  ///
  /// In en, this message translates to:
  /// **'Created At'**
  String get tripDetails_createdAt;

  /// Label for the completed at
  ///
  /// In en, this message translates to:
  /// **'Completed At'**
  String get tripDetails_completedAt;

  /// Label for the cancelled at
  ///
  /// In en, this message translates to:
  /// **'Cancelled At'**
  String get tripDetails_cancelledAt;

  /// Label for the start location
  ///
  /// In en, this message translates to:
  /// **'Start Location'**
  String get tripDetails_startLocation;

  /// Label for the arrival location
  ///
  /// In en, this message translates to:
  /// **'Arrival Location'**
  String get tripDetails_arrivalLocation;

  /// Label for the driver location
  ///
  /// In en, this message translates to:
  /// **'Driver Location'**
  String get tripDetails_driverLocation;

  /// Button text to show the trip on the map
  ///
  /// In en, this message translates to:
  /// **'Show on the Map'**
  String get tripDetails_showOnMap;

  /// Title for the location permission dialog
  ///
  /// In en, this message translates to:
  /// **'Location Permission Required'**
  String get mapScreen_locationPermissionTitle;

  /// Location permission message for driver mode
  ///
  /// In en, this message translates to:
  /// **'Please allow \"Always\" location permission for the \"Fiaranow\" app to work properly in driver mode.'**
  String get mapScreen_locationPermissionMessageDriver;

  /// Location permission message for rider mode
  ///
  /// In en, this message translates to:
  /// **'Please allow location permission for the \"Fiaranow\" app to work properly.'**
  String get mapScreen_locationPermissionMessageRider;

  /// Button text to enable location services
  ///
  /// In en, this message translates to:
  /// **'Enable Location'**
  String get mapScreen_enableLocationButton;

  /// Title shown when device location services are disabled
  ///
  /// In en, this message translates to:
  /// **'Location Services Disabled'**
  String get mapScreen_locationServiceDisabledTitle;

  /// Message explaining that location services must be enabled
  ///
  /// In en, this message translates to:
  /// **'Location services are turned off on your device. Fiaranow requires location services to function. Please enable location services in your device settings.'**
  String get mapScreen_locationServiceDisabledMessage;

  /// Button to open device location settings
  ///
  /// In en, this message translates to:
  /// **'Open Location Settings'**
  String get mapScreen_openLocationSettingsButton;

  /// Button text for trip reservation
  ///
  /// In en, this message translates to:
  /// **'Reserve this Trip'**
  String get mapScreen_reserveThisTrip;

  /// Title for route selection
  ///
  /// In en, this message translates to:
  /// **'Select a Route ({count})'**
  String mapScreen_routeSelectionTitle(int count);

  /// Title for route reservation
  ///
  /// In en, this message translates to:
  /// **'Reserve a Route ({count})'**
  String mapScreen_routeReserveTitle(int count);

  /// Label for reservation date field
  ///
  /// In en, this message translates to:
  /// **'Date'**
  String get mapScreen_reservationDateLabel;

  /// Label for reservation time field
  ///
  /// In en, this message translates to:
  /// **'Time'**
  String get mapScreen_reservationTimeLabel;

  /// Button text to confirm reservation
  ///
  /// In en, this message translates to:
  /// **'Confirm Reservation'**
  String get mapScreen_confirmReservation;

  /// Label for estimated trip cost
  ///
  /// In en, this message translates to:
  /// **'Estimated Trip Cost: '**
  String get mapScreen_estimatedTripCost;

  /// Label for the final trip cost
  ///
  /// In en, this message translates to:
  /// **'Final Trip Cost:'**
  String get mapScreen_finalTripCost;

  /// Label for the estimated trip cost
  ///
  /// In en, this message translates to:
  /// **'Estimated Trip Cost:'**
  String get mapScreen_currentTripCost;

  /// Text indicating the trip has been cancelled
  ///
  /// In en, this message translates to:
  /// **'Sorry, this trip has been cancelled.'**
  String get mapScreen_sorryTripCancelled;

  /// Text indicating a driver has been assigned
  ///
  /// In en, this message translates to:
  /// **'A driver has been assigned to you.'**
  String get mapScreen_driverAssigned;

  /// Text indicating a driver will be assigned
  ///
  /// In en, this message translates to:
  /// **'A driver will be assigned to you. Please stand-by.'**
  String get mapScreen_driverWillBeAssigned;

  /// Text indicating the driver is approaching
  ///
  /// In en, this message translates to:
  /// **'Driver is approaching you...'**
  String get mapScreen_driverApproaching;

  /// Text indicating the waiting time
  ///
  /// In en, this message translates to:
  /// **'Waiting time:'**
  String get mapScreen_waitingTime;

  /// Text for the start trip button
  ///
  /// In en, this message translates to:
  /// **'Start Trip'**
  String get mapScreen_startTrip;

  /// Text indicating to enjoy the ride
  ///
  /// In en, this message translates to:
  /// **'Enjoy the ride!'**
  String get mapScreen_enjoyTheRide;

  /// Text for thanking the user for riding with Fiaranow
  ///
  /// In en, this message translates to:
  /// **'Thank you for riding with Fiaranow!'**
  String get mapScreen_thankYouForRiding;

  /// Text indicating the trip is paid
  ///
  /// In en, this message translates to:
  /// **'Trip is paid.'**
  String get mapScreen_tripPaid;

  /// Title for reserving a route
  ///
  /// In en, this message translates to:
  /// **'Reserve a Route ({count})'**
  String mapScreen_reserveRoute(int count);

  /// Title for selecting a route
  ///
  /// In en, this message translates to:
  /// **'Select a Route ({count})'**
  String mapScreen_selectRoute(int count);

  /// Warning message about potential route changes
  ///
  /// In en, this message translates to:
  /// **'During the Trip, the driver could choose a different route depending on the road conditions, traffic, or upon Your request. The Trip cost will update accordingly.'**
  String get mapScreen_routeWarningMessage;

  /// Route number label
  ///
  /// In en, this message translates to:
  /// **'Route {number}'**
  String mapScreen_route(int number);

  /// Route details text
  ///
  /// In en, this message translates to:
  /// **'Duration: {duration}, Distance: {distance} km, Cost: {cost} Ar'**
  String mapScreen_routeDetails(String duration, String distance, String cost);

  /// Snackbar title for success
  ///
  /// In en, this message translates to:
  /// **'Success'**
  String get mapScreen_success;

  /// Snackbar message for successful trip reservation
  ///
  /// In en, this message translates to:
  /// **'Trip reserved successfully.'**
  String get mapScreen_tripReservedSuccessfully;

  /// Error message title
  ///
  /// In en, this message translates to:
  /// **'Error'**
  String get mapScreen_error;

  /// Error message shown when driver is already assigned to another trip
  ///
  /// In en, this message translates to:
  /// **'You are already assigned to another trip. Please complete or cancel your current trip first.'**
  String get mapScreen_driverAlreadyAssigned;

  /// Error message shown when driver doesn't meet availability requirements
  ///
  /// In en, this message translates to:
  /// **'You do not meet the availability requirements. Please check your documents and vehicle status.'**
  String get mapScreen_driverNotAvailable;

  /// Error message shown when trip is not found or no longer available
  ///
  /// In en, this message translates to:
  /// **'This trip is no longer available.'**
  String get mapScreen_tripNotFound;

  /// Error message shown when driver's location is not available
  ///
  /// In en, this message translates to:
  /// **'Your location is not available. Please enable location services and try again.'**
  String get mapScreen_locationNotAvailable;

  /// Message shown when selected time is too soon
  ///
  /// In en, this message translates to:
  /// **'Please select a time at least 15 minutes in the future.'**
  String get mapScreen_selectFutureTime;

  /// Title of the exit confirmation dialog
  ///
  /// In en, this message translates to:
  /// **'Exit Application'**
  String get exit_dialog_title;

  /// Message shown in the exit confirmation dialog
  ///
  /// In en, this message translates to:
  /// **'Are you sure you want to exit the application?'**
  String get exit_dialog_message;

  /// Title for payment method selection dialog
  ///
  /// In en, this message translates to:
  /// **'Choose a Payment Method'**
  String get mapScreen_choosePaymentMethod;

  /// Option for cash payment
  ///
  /// In en, this message translates to:
  /// **'Cash Payment'**
  String get mapScreen_cashPayment;

  /// Option for mobile money payment
  ///
  /// In en, this message translates to:
  /// **'Mobile Money Payment'**
  String get mapScreen_mobileMoneyPayment;

  /// Title for ride type selection dialog
  ///
  /// In en, this message translates to:
  /// **'Choose Ride Type'**
  String get mapScreen_chooseRideType;

  /// Option to reserve a ride
  ///
  /// In en, this message translates to:
  /// **'Reserve a ride'**
  String get mapScreen_reserveRide;

  /// Subtitle for reserve ride option
  ///
  /// In en, this message translates to:
  /// **'Choose date and time'**
  String get mapScreen_chooseDateAndTime;

  /// Option to ride now
  ///
  /// In en, this message translates to:
  /// **'Ride now'**
  String get mapScreen_rideNow;

  /// Subtitle for ride now option
  ///
  /// In en, this message translates to:
  /// **'Find available drivers'**
  String get mapScreen_findAvailableDrivers;

  /// Title shown when device clock has significant drift
  ///
  /// In en, this message translates to:
  /// **'Your device clock is inaccurate'**
  String get mapScreen_deviceClockInaccurate;

  /// Message asking user to adjust time settings
  ///
  /// In en, this message translates to:
  /// **'Please adjust your device time settings to automatic to continue using the app.'**
  String get mapScreen_adjustDeviceTimeSettings;

  /// Shows the current time drift in seconds
  ///
  /// In en, this message translates to:
  /// **'Current time difference: {seconds} seconds'**
  String mapScreen_currentTimeDifference(String seconds);

  /// Warning message for smaller clock drift
  ///
  /// In en, this message translates to:
  /// **'Your device clock is off by {seconds} seconds. Consider enabling automatic time settings.'**
  String mapScreen_deviceClockOff(String seconds);

  /// Button text to dismiss the device clock warning dialog
  ///
  /// In en, this message translates to:
  /// **'Dismiss'**
  String get mapScreen_dismissClockWarning;

  /// Error message shown when route fetching fails
  ///
  /// In en, this message translates to:
  /// **'Failed to get routes. Please check your Internet connection.'**
  String get mapScreen_failedToGetRoutes;

  /// Title for starting the service
  ///
  /// In en, this message translates to:
  /// **'Start Service'**
  String get serviceStatusUpdate_startService;

  /// Title for stopping the service
  ///
  /// In en, this message translates to:
  /// **'Stop Service'**
  String get serviceStatusUpdate_stopService;

  /// Prompt asking why the service is being started
  ///
  /// In en, this message translates to:
  /// **'Why are you starting your service?'**
  String get serviceStatusUpdate_whyStarting;

  /// Prompt asking why the service is being stopped
  ///
  /// In en, this message translates to:
  /// **'Why are you stopping your service?'**
  String get serviceStatusUpdate_whyStopping;

  /// Label for the reason input field
  ///
  /// In en, this message translates to:
  /// **'Reason'**
  String get serviceStatusUpdate_reason;

  /// Prompt to specify a custom reason
  ///
  /// In en, this message translates to:
  /// **'Please specify your reason'**
  String get serviceStatusUpdate_specifyReason;

  /// Reason for starting service in the morning
  ///
  /// In en, this message translates to:
  /// **'Morning Service Start'**
  String get serviceStatusReason_morningServiceStart;

  /// Reason for starting service in the evening
  ///
  /// In en, this message translates to:
  /// **'Evening Service Start'**
  String get serviceStatusReason_eveningServiceStart;

  /// Reason for taking a lunch break
  ///
  /// In en, this message translates to:
  /// **'Lunch Break'**
  String get serviceStatusReason_lunchBreak;

  /// Reason for taking a prayer break
  ///
  /// In en, this message translates to:
  /// **'Prayer Break'**
  String get serviceStatusReason_prayerBreak;

  /// Reason for refilling fuel
  ///
  /// In en, this message translates to:
  /// **'Fuel Refill'**
  String get serviceStatusReason_fuelRefill;

  /// Reason for vehicle maintenance
  ///
  /// In en, this message translates to:
  /// **'Vehicle Maintenance'**
  String get serviceStatusReason_vehicleMaintenance;

  /// Reason for ending the shift
  ///
  /// In en, this message translates to:
  /// **'End of Shift'**
  String get serviceStatusReason_endOfShift;

  /// Reason for an emergency stop
  ///
  /// In en, this message translates to:
  /// **'Emergency Stop'**
  String get serviceStatusReason_emergencyStop;

  /// Reason for switching activity
  ///
  /// In en, this message translates to:
  /// **'Switch Activity'**
  String get serviceStatusReason_switchActivity;

  /// Reason for relaunching the app
  ///
  /// In en, this message translates to:
  /// **'App Relaunch'**
  String get serviceStatusReason_appRelaunch;

  /// Custom reason specified by the user
  ///
  /// In en, this message translates to:
  /// **'Custom'**
  String get serviceStatusReason_custom;

  /// Title of the trip rejection screen
  ///
  /// In en, this message translates to:
  /// **'Trip Rejection'**
  String get tripRejectionScreen_title;

  /// Message asking the driver to select a reason for rejecting the trip
  ///
  /// In en, this message translates to:
  /// **'Please select a reason for rejecting this trip:'**
  String get tripRejectionScreen_selectReason;

  /// Option for rejecting trip due to vehicle malfunction
  ///
  /// In en, this message translates to:
  /// **'Vehicle malfunction'**
  String get tripRejectionScreen_vehicleMalfunction;

  /// Option for rejecting trip due to distant pickup location
  ///
  /// In en, this message translates to:
  /// **'Pick-up location too far'**
  String get tripRejectionScreen_tooFarPickup;

  /// Option for rejecting trip due to heavy traffic
  ///
  /// In en, this message translates to:
  /// **'Heavy traffic in the area'**
  String get tripRejectionScreen_heavyTraffic;

  /// Option for rejecting trip due to unsafe area
  ///
  /// In en, this message translates to:
  /// **'Unsafe area'**
  String get tripRejectionScreen_unsafeArea;

  /// Option for rejecting trip due to ending shift
  ///
  /// In en, this message translates to:
  /// **'Ending shift soon'**
  String get tripRejectionScreen_endingShiftSoon;

  /// Option for rejecting trip due to vehicle needing cleaning
  ///
  /// In en, this message translates to:
  /// **'Vehicle needs cleaning'**
  String get tripRejectionScreen_vehicleCleaning;

  /// Option for rejecting trip due to full passenger capacity
  ///
  /// In en, this message translates to:
  /// **'Passenger capacity full'**
  String get tripRejectionScreen_passengerCapacityFull;

  /// Option for rejecting trip due to low battery
  ///
  /// In en, this message translates to:
  /// **'Battery low'**
  String get tripRejectionScreen_batteryLow;

  /// Option for rejecting trip due to bad weather
  ///
  /// In en, this message translates to:
  /// **'Bad weather conditions'**
  String get tripRejectionScreen_weatherConditions;

  /// Option for rejecting trip with a custom reason
  ///
  /// In en, this message translates to:
  /// **'Other reason (specify)'**
  String get tripRejectionScreen_custom;

  /// Label for the custom reason text input field
  ///
  /// In en, this message translates to:
  /// **'Please specify your reason'**
  String get tripRejectionScreen_customReasonLabel;

  /// Text for the confirm rejection button
  ///
  /// In en, this message translates to:
  /// **'Confirm Rejection'**
  String get tripRejectionScreen_confirm;

  /// Title for error messages
  ///
  /// In en, this message translates to:
  /// **'Error'**
  String get tripRejectionScreen_error;

  /// Error message shown when no custom reason is provided
  ///
  /// In en, this message translates to:
  /// **'Please enter your reason for rejection'**
  String get tripRejectionScreen_pleaseEnterReason;

  /// Error message shown when canceling a request fails
  ///
  /// In en, this message translates to:
  /// **'Failed to cancel the request. Please try again.'**
  String get mapScreen_cancelRequestFailed;

  /// Text showing both pickup and trip distances
  ///
  /// In en, this message translates to:
  /// **'Pickup & Trip distance: {distance} km'**
  String mapScreen_pickupAndTripDistance(String distance);

  /// Text showing just the pickup distance
  ///
  /// In en, this message translates to:
  /// **'Pickup distance: {distance} km'**
  String mapScreen_pickupDistance(String distance);

  /// Label for the pickup location
  ///
  /// In en, this message translates to:
  /// **'Pick-up location'**
  String get mapScreen_pickupLocation;

  /// Label for the scheduled pickup time
  ///
  /// In en, this message translates to:
  /// **'Pickup Time'**
  String get mapScreen_pickupTime;

  /// Label for the destination location
  ///
  /// In en, this message translates to:
  /// **'Destination location'**
  String get mapScreen_destinationLocation;

  /// Section header for profile settings in the menu drawer
  ///
  /// In en, this message translates to:
  /// **'Profile Settings'**
  String get menuDrawer_profileSettings;

  /// Label for the current user mode (driver/rider) setting
  ///
  /// In en, this message translates to:
  /// **'Current mode'**
  String get menuDrawer_currentMode;

  /// Label for the phone number setting
  ///
  /// In en, this message translates to:
  /// **'Phone number'**
  String get menuDrawer_phoneNumber;

  /// Section header for permissions in the menu drawer
  ///
  /// In en, this message translates to:
  /// **'Permissions'**
  String get menuDrawer_permissions;

  /// Label for push notifications permission setting
  ///
  /// In en, this message translates to:
  /// **'Push Notifications'**
  String get menuDrawer_pushNotifications;

  /// Menu item for GPS location permission
  ///
  /// In en, this message translates to:
  /// **'GPS Location'**
  String get menuDrawer_gpsLocation;

  /// Menu item for GPS location permission (background for drivers)
  ///
  /// In en, this message translates to:
  /// **'GPS Location (Background)'**
  String get menuDrawer_gpsLocationBackground;

  /// Subtitle for GPS location when driver has 'while in use' permission.
  ///
  /// In en, this message translates to:
  /// **'Set to \'Always\' for optimal driver experience.'**
  String get menuDrawer_locationWhileInUseDriverSubtitle;

  /// Menu item for drivers to access their documents
  ///
  /// In en, this message translates to:
  /// **'My Documents'**
  String get menuDrawer_driverDocuments;

  /// Subtitle description for driver documents menu item
  ///
  /// In en, this message translates to:
  /// **'Upload and manage your documents'**
  String get menuDrawer_driverDocumentsDesc;

  /// Message in UpdateRequiredScreen indicating that an update is required to continue using the app
  ///
  /// In en, this message translates to:
  /// **'An update is required to continue using this app.'**
  String get updateRequiredScreen_message;

  /// Button text in UpdateRequiredScreen to initiate the update process
  ///
  /// In en, this message translates to:
  /// **'Update Now'**
  String get updateRequiredScreen_updateNow;

  /// Title for the full day reservation dialog
  ///
  /// In en, this message translates to:
  /// **'Full Day Reservation'**
  String get mapScreen_fullDayReservation;

  /// Message explaining what a full day reservation is
  ///
  /// In en, this message translates to:
  /// **'A full day reservation allows you to have a driver at your disposal for the entire day. The driver will pick you up at the specified time and location, and will be available to drive you wherever you need to go throughout the day.'**
  String get mapScreen_fullDayReservationPrompt;

  /// Title for the full day price options dialog
  ///
  /// In en, this message translates to:
  /// **'Choose Price Option'**
  String get mapScreen_fullDayPriceOptions;

  /// Option for full day reservation with gas included
  ///
  /// In en, this message translates to:
  /// **'€75 (including gas)'**
  String get mapScreen_fullDayFixedPrice;

  /// Text indicating that gas is included in the price
  ///
  /// In en, this message translates to:
  /// **'Gas included in price'**
  String get mapScreen_gasIncluded;

  /// Option for full day reservation without gas
  ///
  /// In en, this message translates to:
  /// **'€25 (gas not included)'**
  String get mapScreen_fullDayGasExcluded;

  /// Text indicating that gas is not included in the price
  ///
  /// In en, this message translates to:
  /// **'You pay for gas separately'**
  String get mapScreen_gasNotIncluded;

  /// Success message shown after creating a full day reservation
  ///
  /// In en, this message translates to:
  /// **'Full day reservation created successfully!'**
  String get mapScreen_fullDayReservationSuccess;

  /// Title for the dialog shown when account is already logged in on another device
  ///
  /// In en, this message translates to:
  /// **'Account Already in Use'**
  String get auth_accountInUseTitle;

  /// Message asking if user wants to log out from other devices
  ///
  /// In en, this message translates to:
  /// **'Your account is already logged in on another device. Do you want to log out from other devices?'**
  String get auth_accountInUseMessage;

  /// Button text to confirm logging out from other devices
  ///
  /// In en, this message translates to:
  /// **'Yes'**
  String get auth_logoutOtherDevices;

  /// Button text to cancel login and not log out from other devices
  ///
  /// In en, this message translates to:
  /// **'No'**
  String get auth_cancelLogin;

  /// Title for the dialog shown when user is forced to log out
  ///
  /// In en, this message translates to:
  /// **'Logged Out'**
  String get auth_forcedLogoutTitle;

  /// Message shown when user is forced to log out because of login on another device
  ///
  /// In en, this message translates to:
  /// **'Your account has been logged in on another device.'**
  String get auth_forcedLogoutMessage;

  /// Button text to acknowledge forced logout
  ///
  /// In en, this message translates to:
  /// **'OK'**
  String get auth_forcedLogoutButton;

  /// Trip status when the trip is being prepared
  ///
  /// In en, this message translates to:
  /// **'Preparing'**
  String get tripStatus_preparing;

  /// Trip status when searching for a driver
  ///
  /// In en, this message translates to:
  /// **'Requesting Driver'**
  String get tripStatus_requestingDriver;

  /// Trip status when the trip is reserved
  ///
  /// In en, this message translates to:
  /// **'Reserved'**
  String get tripStatus_reserved;

  /// Trip status when the driver is approaching
  ///
  /// In en, this message translates to:
  /// **'Driver Approaching'**
  String get tripStatus_driverApproaching;

  /// Trip status when the driver is waiting for passenger
  ///
  /// In en, this message translates to:
  /// **'Driver Awaiting'**
  String get tripStatus_driverAwaiting;

  /// Trip status when the trip is ongoing
  ///
  /// In en, this message translates to:
  /// **'In Progress'**
  String get tripStatus_inProgress;

  /// Trip status when the trip is completed
  ///
  /// In en, this message translates to:
  /// **'Completed'**
  String get tripStatus_completed;

  /// Trip status when the trip is cancelled
  ///
  /// In en, this message translates to:
  /// **'Cancelled'**
  String get tripStatus_cancelled;

  /// Trip status when the trip is paid
  ///
  /// In en, this message translates to:
  /// **'Paid'**
  String get tripStatus_paid;

  /// Label for the pricing option in trip details
  ///
  /// In en, this message translates to:
  /// **'Pricing Option'**
  String get tripDetails_pricingOption;

  /// Title for the price details section in trip details
  ///
  /// In en, this message translates to:
  /// **'Price Details'**
  String get tripDetails_priceDetails;

  /// Title for the location details section in trip details
  ///
  /// In en, this message translates to:
  /// **'Location Details'**
  String get tripDetails_locationDetails;

  /// Text shown when the destination is unknown
  ///
  /// In en, this message translates to:
  /// **'Unknown destination'**
  String get tripDetails_unknownDestination;

  /// Button text to delete a trip
  ///
  /// In en, this message translates to:
  /// **'Delete Trip'**
  String get tripDetails_deleteTrip;

  /// Message displayed when the user has no trips in their history
  ///
  /// In en, this message translates to:
  /// **'No trips yet'**
  String get history_noTripsYet;

  /// Title for the trip history screen
  ///
  /// In en, this message translates to:
  /// **'History'**
  String get history_title;

  /// A welcome message on the home screen
  ///
  /// In en, this message translates to:
  /// **'Welcome'**
  String get home_welcome;

  /// Text shown to passengers during a full day reservation trip
  ///
  /// In en, this message translates to:
  /// **'Enjoy your full day ride!'**
  String get mapScreen_enjoyFullDayRide;

  /// Fallback text when pickup location name is not available
  ///
  /// In en, this message translates to:
  /// **'Location'**
  String get mapScreen_locationFallback;

  /// Fallback text when destination location name is not available
  ///
  /// In en, this message translates to:
  /// **'Destination'**
  String get mapScreen_destinationFallback;

  /// Title for the snackbar shown when there is a network error during login
  ///
  /// In en, this message translates to:
  /// **'Network Error'**
  String get auth_networkErrorTitle;

  /// Message shown when login fails due to network connectivity issues
  ///
  /// In en, this message translates to:
  /// **'Unable to complete login due to network issues. Please try again when you have a stable connection.'**
  String get auth_networkErrorMessage;

  /// General error title used in error messages
  ///
  /// In en, this message translates to:
  /// **'Error'**
  String get navigationState_error;

  /// Error message when there's an issue with listening to driver trip requests
  ///
  /// In en, this message translates to:
  /// **'Error listening to driver trip requests. Please restart the app.'**
  String get navigationState_driverTripRequestsError;

  /// Title for location-related error messages
  ///
  /// In en, this message translates to:
  /// **'Location Error'**
  String get navigationState_locationError;

  /// Error message shown when location tracking fails
  ///
  /// In en, this message translates to:
  /// **'Unable to track your location. Please check your location settings and restart the app.'**
  String get navigationState_locationTrackingError;

  /// Title for connection status messages
  ///
  /// In en, this message translates to:
  /// **'Connection Status'**
  String get appState_connectionStatusTitle;

  /// Message shown when internet connection is restored
  ///
  /// In en, this message translates to:
  /// **'You are connected to the Internet.'**
  String get appState_connectionRestored;

  /// Message shown when internet connection is lost or unstable
  ///
  /// In en, this message translates to:
  /// **'Your Internet connection is not doing well right now.'**
  String get appState_connectionLost;

  /// Name of the foreground service notification channel
  ///
  /// In en, this message translates to:
  /// **'Trip Service'**
  String get foregroundService_channelName;

  /// Description of the foreground service notification channel
  ///
  /// In en, this message translates to:
  /// **'Keeps the app alive during an ongoing trip.'**
  String get foregroundService_channelDescription;

  /// Title of the foreground notification shown during a trip
  ///
  /// In en, this message translates to:
  /// **'Trip in progress'**
  String get foregroundService_tripInProgress;

  /// Text shown in the foreground notification during a trip
  ///
  /// In en, this message translates to:
  /// **'Your trip is ongoing. Tap to return to the app.'**
  String get foregroundService_tripOngoing;

  /// Subtitle text for push notifications when permission is permanently denied
  ///
  /// In en, this message translates to:
  /// **'Denied by User'**
  String get menuDrawer_notificationPermanentlyDenied;

  /// Error title shown in snackbar when trip action fails
  ///
  /// In en, this message translates to:
  /// **'Error'**
  String get tripActionButton_error;

  /// Label for cancel trip button
  ///
  /// In en, this message translates to:
  /// **'Cancel'**
  String get tripActionButton_cancel;

  /// Label for start trip button
  ///
  /// In en, this message translates to:
  /// **'Start'**
  String get tripActionButton_start;

  /// Label for complete trip button
  ///
  /// In en, this message translates to:
  /// **'Complete'**
  String get tripActionButton_complete;

  /// Label for help tab in bottom navigation
  ///
  /// In en, this message translates to:
  /// **'Help'**
  String get mainPage_chat;

  /// Title for chat list screen
  ///
  /// In en, this message translates to:
  /// **'Support Chats'**
  String get chat_title;

  /// Button label for creating new chat
  ///
  /// In en, this message translates to:
  /// **'New Chat'**
  String get chat_new;

  /// Empty state message when no chats exist
  ///
  /// In en, this message translates to:
  /// **'No conversations yet'**
  String get chat_no_conversations;

  /// Hint text shown when no chats exist
  ///
  /// In en, this message translates to:
  /// **'Start a new conversation to get help'**
  String get chat_start_conversation_hint;

  /// Title for new chat dialog
  ///
  /// In en, this message translates to:
  /// **'Start New Conversation'**
  String get chat_new_dialog_title;

  /// Label for chat subject/title field
  ///
  /// In en, this message translates to:
  /// **'Subject'**
  String get chat_subject;

  /// Label for chat category dropdown
  ///
  /// In en, this message translates to:
  /// **'Category'**
  String get chat_category;

  /// Button label to start new chat
  ///
  /// In en, this message translates to:
  /// **'Start Chat'**
  String get chat_start;

  /// General support category
  ///
  /// In en, this message translates to:
  /// **'General Support'**
  String get chat_category_general;

  /// Trip-related support category
  ///
  /// In en, this message translates to:
  /// **'Trip Support'**
  String get chat_category_trip;

  /// Payment-related support category
  ///
  /// In en, this message translates to:
  /// **'Payment Support'**
  String get chat_category_payment;

  /// Technical support category
  ///
  /// In en, this message translates to:
  /// **'Technical Support'**
  String get chat_category_technical;

  /// Feedback follow-up category
  ///
  /// In en, this message translates to:
  /// **'Feedback Follow-up'**
  String get chat_category_feedback;

  /// Button text to navigate to trip details from chat
  ///
  /// In en, this message translates to:
  /// **'Trip'**
  String get chat_trip_button;

  /// Label for linked feedback in chat
  ///
  /// In en, this message translates to:
  /// **'Related Feedback'**
  String get related_feedback;

  /// Title for trip feedback screen
  ///
  /// In en, this message translates to:
  /// **'Trip Feedback'**
  String get trip_feedback_title;

  /// Button text to open trip feedback screen
  ///
  /// In en, this message translates to:
  /// **'Give Feedback'**
  String get trip_feedback_button;

  /// Label for trip details section
  ///
  /// In en, this message translates to:
  /// **'Trip Details'**
  String get trip_details;

  /// Header for rating section
  ///
  /// In en, this message translates to:
  /// **'Rate Your Trip'**
  String get rate_your_trip;

  /// Label for feedback message field
  ///
  /// In en, this message translates to:
  /// **'Feedback Message'**
  String get feedback_message;

  /// Placeholder for feedback message field
  ///
  /// In en, this message translates to:
  /// **'Tell us about your experience...'**
  String get feedback_message_hint;

  /// Label for photo addition section
  ///
  /// In en, this message translates to:
  /// **'Add Photos (Optional)'**
  String get add_photos;

  /// Message shown when feedback already exists
  ///
  /// In en, this message translates to:
  /// **'You have already submitted feedback for this trip'**
  String get feedback_already_submitted;

  /// Button label to submit feedback
  ///
  /// In en, this message translates to:
  /// **'Submit Feedback'**
  String get submit_feedback;

  /// Error message when max images reached
  ///
  /// In en, this message translates to:
  /// **'Maximum 5 images allowed'**
  String get max_images_reached;

  /// Error message when rating not selected
  ///
  /// In en, this message translates to:
  /// **'Please rate your trip'**
  String get please_rate_trip;

  /// Success message after feedback submission
  ///
  /// In en, this message translates to:
  /// **'Feedback submitted successfully'**
  String get feedback_submitted_success;

  /// Title for application feedback screen
  ///
  /// In en, this message translates to:
  /// **'Application Feedback'**
  String get app_feedback_title;

  /// Description for app feedback screen
  ///
  /// In en, this message translates to:
  /// **'Help us improve the app by sharing your feedback, bug reports, or suggestions.'**
  String get app_feedback_description;

  /// Placeholder for app feedback message field
  ///
  /// In en, this message translates to:
  /// **'Describe the issue or suggestion in detail...'**
  String get app_feedback_hint;

  /// Label for screenshot section
  ///
  /// In en, this message translates to:
  /// **'Current Screen Screenshot'**
  String get current_screen_screenshot;

  /// Checkbox label for including screenshot
  ///
  /// In en, this message translates to:
  /// **'Include screenshot'**
  String get include_screenshot;

  /// Label for additional images section
  ///
  /// In en, this message translates to:
  /// **'Additional Images'**
  String get additional_images;

  /// Helper text for image limits
  ///
  /// In en, this message translates to:
  /// **'Maximum 5 images (5MB each)'**
  String get max_5_images;

  /// Error message for large images
  ///
  /// In en, this message translates to:
  /// **'Image size must be less than 5MB'**
  String get image_too_large;

  /// Error when message is empty
  ///
  /// In en, this message translates to:
  /// **'Please enter a message'**
  String get please_enter_message;

  /// Generic error message with details
  ///
  /// In en, this message translates to:
  /// **'Error: {details}'**
  String error_with_details(String details);

  /// Error message when chat creation fails
  ///
  /// In en, this message translates to:
  /// **'Error creating chat: {error}'**
  String error_creating_chat(String error);

  /// Error message when loading feedback fails
  ///
  /// In en, this message translates to:
  /// **'Error loading linked feedback: {error}'**
  String error_loading_feedback(String error);

  /// Error message when image picking fails
  ///
  /// In en, this message translates to:
  /// **'Error picking image: {error}'**
  String error_picking_image(String error);

  /// Error message when image sending fails
  ///
  /// In en, this message translates to:
  /// **'Error sending image: {error}'**
  String error_sending_image(String error);

  /// Error message when checking feedback fails
  ///
  /// In en, this message translates to:
  /// **'Error checking existing feedback: {error}'**
  String error_checking_feedback(String error);

  /// Error message when image upload fails
  ///
  /// In en, this message translates to:
  /// **'Error uploading image: {error}'**
  String error_uploading_image(String error);

  /// Error message when feedback submission fails
  ///
  /// In en, this message translates to:
  /// **'Error submitting feedback: {error}'**
  String error_submitting_feedback(String error);

  /// Error message when screenshot capture fails
  ///
  /// In en, this message translates to:
  /// **'Error capturing screenshot: {error}'**
  String error_capturing_screenshot(String error);

  /// Settings menu item
  ///
  /// In en, this message translates to:
  /// **'Settings'**
  String get settings;

  /// Notification settings screen title
  ///
  /// In en, this message translates to:
  /// **'Notification Settings'**
  String get notificationSettings;

  /// General settings section
  ///
  /// In en, this message translates to:
  /// **'General'**
  String get settings_general;

  /// Trip notifications section
  ///
  /// In en, this message translates to:
  /// **'Trip Notifications'**
  String get tripNotifications;

  /// Ringtone notifications setting
  ///
  /// In en, this message translates to:
  /// **'Ringtone Notifications'**
  String get ringtoneNotifications;

  /// Ringtone notifications description
  ///
  /// In en, this message translates to:
  /// **'Play a ringtone for important notifications'**
  String get ringtoneNotificationsDesc;

  /// Shows when using admin default setting which is enabled in main page
  ///
  /// In en, this message translates to:
  /// **'Using default (enabled)'**
  String get mainPage_usingDefaultEnabled;

  /// Shows when using admin default setting which is disabled in main page
  ///
  /// In en, this message translates to:
  /// **'Using default (disabled)'**
  String get mainPage_usingDefaultDisabled;

  /// Driver moving notification setting in main page
  ///
  /// In en, this message translates to:
  /// **'Driver Moving'**
  String get mainPage_driverMoving;

  /// Driver moving notification description in main page
  ///
  /// In en, this message translates to:
  /// **'Get notified when your driver starts heading to pick you up'**
  String get mainPage_driverMovingDesc;

  /// Driver arrived notification setting in main page
  ///
  /// In en, this message translates to:
  /// **'Driver Arrived'**
  String get mainPage_driverArrived;

  /// Driver arrived notification description in main page
  ///
  /// In en, this message translates to:
  /// **'Get notified when your driver has arrived at pickup location'**
  String get mainPage_driverArrivedDesc;

  /// Payment completed notification setting in main page
  ///
  /// In en, this message translates to:
  /// **'Payment Completed'**
  String get mainPage_paymentCompleted;

  /// Payment completed notification description in main page
  ///
  /// In en, this message translates to:
  /// **'Get notified when your trip payment is processed'**
  String get mainPage_paymentCompletedDesc;

  /// Reservations section in main page
  ///
  /// In en, this message translates to:
  /// **'Reservations'**
  String get mainPage_reservations;

  /// Reservation reminders setting in main page
  ///
  /// In en, this message translates to:
  /// **'Reservation Reminders'**
  String get mainPage_reservationReminders;

  /// Reservation reminders description in main page
  ///
  /// In en, this message translates to:
  /// **'Get reminders before your scheduled trips'**
  String get mainPage_reservationRemindersDesc;

  /// Title for ringtone permission dialog in main page
  ///
  /// In en, this message translates to:
  /// **'Enable Ringtone Notifications?'**
  String get mainPage_ringtonePermissionTitle;

  /// Message for ringtone permission dialog in main page
  ///
  /// In en, this message translates to:
  /// **'Would you like to receive important trip notifications with a ringtone?'**
  String get mainPage_ringtonePermissionMessage;

  /// Description for ringtone permission dialog in main page
  ///
  /// In en, this message translates to:
  /// **'This includes notifications when your driver is on the way or has arrived.'**
  String get mainPage_ringtonePermissionDescription;

  /// No thanks button in main page
  ///
  /// In en, this message translates to:
  /// **'No Thanks'**
  String get mainPage_noThanks;

  /// Enable ringtone button in main page
  ///
  /// In en, this message translates to:
  /// **'Enable Ringtone'**
  String get mainPage_enableRingtone;

  /// Title for the passenger count slider
  ///
  /// In en, this message translates to:
  /// **'Number of Passengers'**
  String get passengerCountSlider_title;

  /// Title for the add vehicle screen
  ///
  /// In en, this message translates to:
  /// **'Add Vehicle'**
  String get addVehicle_title;

  /// Message when no documents are uploaded
  ///
  /// In en, this message translates to:
  /// **'No documents uploaded yet'**
  String get no_documents_uploaded;

  /// Message when no vehicles are added
  ///
  /// In en, this message translates to:
  /// **'No vehicles added yet'**
  String get no_vehicles_added;

  /// Shows the vehicle capacity in passengers
  ///
  /// In en, this message translates to:
  /// **'Capacity: {maxPassengers} passengers'**
  String vehicle_capacity_passengers(int maxPassengers);

  /// Label for currently assigned vehicle badge
  ///
  /// In en, this message translates to:
  /// **'Currently Assigned'**
  String get vehicle_currentlyAssigned;

  /// Error message for vehicle list
  ///
  /// In en, this message translates to:
  /// **'Error: {error}'**
  String vehicle_error(String error);

  /// Section header for assigned vehicle
  ///
  /// In en, this message translates to:
  /// **'Assigned Vehicle'**
  String get vehicle_assignedVehicle;

  /// Section header for user's vehicles
  ///
  /// In en, this message translates to:
  /// **'My Vehicles'**
  String get vehicle_myVehicles;

  /// Shows how many days until document expires
  ///
  /// In en, this message translates to:
  /// **'Expires in {days} days'**
  String document_expiresInDays(int days);

  /// Shows the expiry date of a document
  ///
  /// In en, this message translates to:
  /// **'Expires: {date}'**
  String document_expiresOn(String date);

  /// Welcome message in the menu drawer
  ///
  /// In en, this message translates to:
  /// **'Welcome'**
  String get menuDrawer_welcome;

  /// Logout button in main page
  ///
  /// In en, this message translates to:
  /// **'Logout'**
  String get mainPage_logout;

  /// Menu label in main page
  ///
  /// In en, this message translates to:
  /// **'Menu'**
  String get mainPage_menu;

  /// No button in main page dialogs
  ///
  /// In en, this message translates to:
  /// **'No'**
  String get mainPage_no;

  /// Yes button in main page dialogs
  ///
  /// In en, this message translates to:
  /// **'Yes'**
  String get mainPage_yes;

  /// Save button in main page dialogs
  ///
  /// In en, this message translates to:
  /// **'Save'**
  String get mainPage_save;

  /// Cancel button in main page dialogs
  ///
  /// In en, this message translates to:
  /// **'Cancel'**
  String get mainPage_cancel;

  /// Error message in main page dialogs
  ///
  /// In en, this message translates to:
  /// **'Error'**
  String get mainPage_error;

  /// Settings menu item in main page
  ///
  /// In en, this message translates to:
  /// **'Settings'**
  String get mainPage_settings;

  /// Notification settings screen title in main page
  ///
  /// In en, this message translates to:
  /// **'Notification Settings'**
  String get mainPage_notificationSettings;

  /// Trip notifications section in main page
  ///
  /// In en, this message translates to:
  /// **'Trip Notifications'**
  String get mainPage_tripNotifications;

  /// Ringtone notifications setting in main page
  ///
  /// In en, this message translates to:
  /// **'Ringtone Notifications'**
  String get mainPage_ringtoneNotifications;

  /// Ringtone notifications description in main page
  ///
  /// In en, this message translates to:
  /// **'Play a ringtone for important notifications'**
  String get mainPage_ringtoneNotificationsDesc;

  /// Error message when preferences fail to load
  ///
  /// In en, this message translates to:
  /// **'Failed to load preferences'**
  String get notificationSettings_failedToLoadPreferences;

  /// Instructions when preferences fail to load
  ///
  /// In en, this message translates to:
  /// **'Please check your connection and try again'**
  String get notificationSettings_checkConnectionAndRetry;

  /// Retry button label
  ///
  /// In en, this message translates to:
  /// **'Retry'**
  String get notificationSettings_retry;

  /// Loading message for preferences
  ///
  /// In en, this message translates to:
  /// **'Loading preferences...'**
  String get notificationSettings_loadingPreferences;

  /// General settings section header
  ///
  /// In en, this message translates to:
  /// **'General'**
  String get notificationSettings_general;

  /// Trip notifications section header
  ///
  /// In en, this message translates to:
  /// **'Trip Notifications'**
  String get notificationSettings_tripNotifications;

  /// Reservations section header
  ///
  /// In en, this message translates to:
  /// **'Reservations'**
  String get notificationSettings_reservations;

  /// Driver moving notification setting
  ///
  /// In en, this message translates to:
  /// **'Driver Moving'**
  String get notificationSettings_driverMoving;

  /// Driver moving notification description
  ///
  /// In en, this message translates to:
  /// **'Get notified when your driver starts heading to pick you up'**
  String get notificationSettings_driverMovingDesc;

  /// Driver arrived notification setting
  ///
  /// In en, this message translates to:
  /// **'Driver Arrived'**
  String get notificationSettings_driverArrived;

  /// Driver arrived notification description
  ///
  /// In en, this message translates to:
  /// **'Get notified when your driver has arrived at pickup location'**
  String get notificationSettings_driverArrivedDesc;

  /// Payment completed notification setting
  ///
  /// In en, this message translates to:
  /// **'Payment Completed'**
  String get notificationSettings_paymentCompleted;

  /// Payment completed notification description
  ///
  /// In en, this message translates to:
  /// **'Get notified when your trip payment is processed'**
  String get notificationSettings_paymentCompletedDesc;

  /// Reservation reminders setting
  ///
  /// In en, this message translates to:
  /// **'Reservation Reminders'**
  String get notificationSettings_reservationReminders;

  /// Reservation reminders description
  ///
  /// In en, this message translates to:
  /// **'Get reminders before your scheduled trips'**
  String get notificationSettings_reservationRemindersDesc;

  /// Error message when unable to open document
  ///
  /// In en, this message translates to:
  /// **'Unable to open this document'**
  String get driverDocuments_unableToOpenDocument;

  /// Title for document detail screen
  ///
  /// In en, this message translates to:
  /// **'Document Details'**
  String get documentDetail_title;

  /// Message when document file is not available
  ///
  /// In en, this message translates to:
  /// **'Document not available'**
  String get documentDetail_notAvailable;

  /// Error message when image fails to load
  ///
  /// In en, this message translates to:
  /// **'Failed to load image'**
  String get documentDetail_failedToLoadImage;

  /// Message when document preview is not available
  ///
  /// In en, this message translates to:
  /// **'Document preview not available'**
  String get documentDetail_previewNotAvailable;

  /// Message for unsupported file formats
  ///
  /// In en, this message translates to:
  /// **'Unsupported file format'**
  String get documentDetail_unsupportedFormat;

  /// Message when document is not found or cannot be loaded
  ///
  /// In en, this message translates to:
  /// **'We could not find this document'**
  String get documentDetail_notFound;

  /// Message when document has expired
  ///
  /// In en, this message translates to:
  /// **'This document has expired'**
  String get documentDetail_expired;

  /// Label for document name field
  ///
  /// In en, this message translates to:
  /// **'Document Name'**
  String get documentDetail_documentName;

  /// Label for upload date field
  ///
  /// In en, this message translates to:
  /// **'Upload Date'**
  String get documentDetail_uploadDate;

  /// Label for expiry date field
  ///
  /// In en, this message translates to:
  /// **'Expiry Date'**
  String get documentDetail_expiryDate;

  /// Label for notes field
  ///
  /// In en, this message translates to:
  /// **'Notes'**
  String get documentDetail_notes;

  /// Section header for review information
  ///
  /// In en, this message translates to:
  /// **'Review Information'**
  String get documentDetail_reviewInformation;

  /// Label for reviewed date field
  ///
  /// In en, this message translates to:
  /// **'Reviewed Date'**
  String get documentDetail_reviewedDate;

  /// Label for admin notes field
  ///
  /// In en, this message translates to:
  /// **'Admin Notes'**
  String get documentDetail_adminNotes;

  /// Section header for document preview
  ///
  /// In en, this message translates to:
  /// **'Document Preview'**
  String get documentDetail_documentPreview;

  /// Title for permission denied errors
  ///
  /// In en, this message translates to:
  /// **'Permission Denied'**
  String get documentUpload_permissionDenied;

  /// Message when camera permission is needed
  ///
  /// In en, this message translates to:
  /// **'Camera permission is required to take photos'**
  String get documentUpload_cameraPermissionRequired;

  /// Message when photo library permission is needed
  ///
  /// In en, this message translates to:
  /// **'Photo library permission is required to select photos'**
  String get documentUpload_photoLibraryPermissionRequired;

  /// Message when storage permission is needed for photos
  ///
  /// In en, this message translates to:
  /// **'Storage permission is required to select photos'**
  String get documentUpload_storagePermissionRequiredPhotos;

  /// Message when storage permission is needed for files
  ///
  /// In en, this message translates to:
  /// **'Storage permission is required to select files'**
  String get documentUpload_storagePermissionRequiredFiles;

  /// Title for file too large error
  ///
  /// In en, this message translates to:
  /// **'File Too Large'**
  String get documentUpload_fileTooLarge;

  /// Message about file size limit
  ///
  /// In en, this message translates to:
  /// **'Please select a file smaller than 5MB'**
  String get documentUpload_fileSizeLimit;

  /// Title for platform errors
  ///
  /// In en, this message translates to:
  /// **'Platform Error'**
  String get documentUpload_platformError;

  /// Message for device errors
  ///
  /// In en, this message translates to:
  /// **'Device error: {error}'**
  String documentUpload_deviceError(String error);

  /// Error message when file picking fails
  ///
  /// In en, this message translates to:
  /// **'Failed to pick file. Please try again.'**
  String get documentUpload_failedToPickFile;

  /// Title for camera errors
  ///
  /// In en, this message translates to:
  /// **'Camera Error'**
  String get documentUpload_cameraError;

  /// Error message when image capture fails
  ///
  /// In en, this message translates to:
  /// **'Failed to capture image. Please try again.'**
  String get documentUpload_failedToCaptureImage;

  /// Title when no file is selected
  ///
  /// In en, this message translates to:
  /// **'No File Selected'**
  String get documentUpload_noFileSelected;

  /// Message prompting to select a document
  ///
  /// In en, this message translates to:
  /// **'Please select a document to upload'**
  String get documentUpload_selectDocumentPrompt;

  /// Title when no expiry date is selected
  ///
  /// In en, this message translates to:
  /// **'No Expiry Date'**
  String get documentUpload_noExpiryDate;

  /// Message prompting to select expiry date
  ///
  /// In en, this message translates to:
  /// **'Please select an expiry date for the document'**
  String get documentUpload_selectExpiryDatePrompt;

  /// Success message after document upload
  ///
  /// In en, this message translates to:
  /// **'Document uploaded successfully'**
  String get documentUpload_uploadSuccess;

  /// Title for upload failure
  ///
  /// In en, this message translates to:
  /// **'Upload Failed'**
  String get documentUpload_uploadFailed;

  /// Firebase error message
  ///
  /// In en, this message translates to:
  /// **'Firebase error: {error}'**
  String documentUpload_firebaseError(String error);

  /// Message for unexpected errors
  ///
  /// In en, this message translates to:
  /// **'An unexpected error occurred. Please try again.'**
  String get documentUpload_unexpectedError;

  /// Label for document type field
  ///
  /// In en, this message translates to:
  /// **'Document Type'**
  String get documentUpload_documentType;

  /// Label for document name field
  ///
  /// In en, this message translates to:
  /// **'Document Name'**
  String get documentUpload_documentName;

  /// Example prefix for hints
  ///
  /// In en, this message translates to:
  /// **'e.g., {example}'**
  String documentUpload_examplePrefix(String example);

  /// Validation message for document name
  ///
  /// In en, this message translates to:
  /// **'Please enter a document name'**
  String get documentUpload_enterDocumentName;

  /// Label for expiry date field
  ///
  /// In en, this message translates to:
  /// **'Expiry Date'**
  String get documentUpload_expiryDate;

  /// Placeholder for expiry date picker
  ///
  /// In en, this message translates to:
  /// **'Select expiry date'**
  String get documentUpload_selectExpiryDate;

  /// Label for optional notes field
  ///
  /// In en, this message translates to:
  /// **'Notes (Optional)'**
  String get documentUpload_notesOptional;

  /// Hint for notes field
  ///
  /// In en, this message translates to:
  /// **'Any additional information'**
  String get documentUpload_additionalInfoHint;

  /// Section header for document selection
  ///
  /// In en, this message translates to:
  /// **'Select Document'**
  String get documentUpload_selectDocument;

  /// Button label to choose file
  ///
  /// In en, this message translates to:
  /// **'Choose File'**
  String get documentUpload_chooseFile;

  /// Button label to take photo
  ///
  /// In en, this message translates to:
  /// **'Take Photo'**
  String get documentUpload_takePhoto;

  /// Information about accepted file formats
  ///
  /// In en, this message translates to:
  /// **'Accepted formats: PDF, JPG, PNG (Max 5MB)'**
  String get documentUpload_acceptedFormats;

  /// Cancel button in payment dialog
  ///
  /// In en, this message translates to:
  /// **'Cancel'**
  String get paymentDialog_cancel;

  /// Save button in add vehicle screen
  ///
  /// In en, this message translates to:
  /// **'Save'**
  String get addVehicle_saveButton;

  /// Cancel button in chat dialog
  ///
  /// In en, this message translates to:
  /// **'Cancel'**
  String get chatDialog_cancel;

  /// Yesterday text in chat screen
  ///
  /// In en, this message translates to:
  /// **'Yesterday'**
  String get chat_yesterday;

  /// Days ago text in chat screen
  ///
  /// In en, this message translates to:
  /// **'days ago'**
  String get chat_daysAgo;

  /// Support team label in chat screen
  ///
  /// In en, this message translates to:
  /// **'Support Team'**
  String get chat_supportTeam;

  /// Placeholder text for message input in chat screen
  ///
  /// In en, this message translates to:
  /// **'Type a message...'**
  String get chat_typeMessage;

  /// Camera button in feedback screen
  ///
  /// In en, this message translates to:
  /// **'Camera'**
  String get feedback_camera;

  /// Gallery button in feedback screen
  ///
  /// In en, this message translates to:
  /// **'Gallery'**
  String get feedback_gallery;

  /// Text shown when location is unknown in trip details
  ///
  /// In en, this message translates to:
  /// **'Unknown'**
  String get tripDetails_locationUnknown;

  /// Fixed price label in trip details
  ///
  /// In en, this message translates to:
  /// **'Carburant inclus'**
  String get tripDetails_fixedPrice;

  /// Per hour label in trip details
  ///
  /// In en, this message translates to:
  /// **'Carburant en sus'**
  String get tripDetails_perHour;

  /// Title for driver documents screen
  ///
  /// In en, this message translates to:
  /// **'My Documents'**
  String get driverDocuments_title;

  /// Upload document button text
  ///
  /// In en, this message translates to:
  /// **'Upload Document'**
  String get documentUpload_uploadButton;

  /// Title for vehicle management screen
  ///
  /// In en, this message translates to:
  /// **'My Vehicles'**
  String get vehicleManagement_title;

  /// Add vehicle button text
  ///
  /// In en, this message translates to:
  /// **'Add Vehicle'**
  String get vehicleManagement_addButton;

  /// Title for document upload screen
  ///
  /// In en, this message translates to:
  /// **'Upload Document'**
  String get documentUpload_title;

  /// Text displayed for drivers who have no ratings yet
  ///
  /// In en, this message translates to:
  /// **'New Driver'**
  String get driverRating_newDriver;

  /// Error message when place predictions fail to load
  ///
  /// In en, this message translates to:
  /// **'Failed to get place predictions. Please check your Internet connection.'**
  String get mapScreen_failedToGetPredictions;

  /// Error message when place details fail to load
  ///
  /// In en, this message translates to:
  /// **'Failed to get place details. Please check your Internet connection.'**
  String get mapScreen_failedToGetPlaceDetails;

  /// Loading status message for map initialization
  ///
  /// In en, this message translates to:
  /// **'Loading map...'**
  String get mapScreen_loadingMap;

  /// Message shown while trip cost is being calculated
  ///
  /// In en, this message translates to:
  /// **'Calculating cost...'**
  String get passengerTripControl_calculatingCost;

  /// Message shown when viewing a non-active trip
  ///
  /// In en, this message translates to:
  /// **'Viewing {status} trip'**
  String passengerTripControl_viewingTrip(String status);

  /// Title for delete confirmation dialog
  ///
  /// In en, this message translates to:
  /// **'Confirm Delete'**
  String get tripDetails_confirmDelete;

  /// Message for delete confirmation dialog
  ///
  /// In en, this message translates to:
  /// **'Are you sure you want to delete this trip?'**
  String get tripDetails_confirmDeleteMessage;

  /// Cancel button text in delete confirmation dialog
  ///
  /// In en, this message translates to:
  /// **'Cancel'**
  String get tripDetails_cancelButton;

  /// Delete button text in delete confirmation dialog
  ///
  /// In en, this message translates to:
  /// **'Delete'**
  String get tripDetails_deleteButton;

  /// Error message when trying to view a trip while having an active trip
  ///
  /// In en, this message translates to:
  /// **'Cannot show trip while you have an active trip'**
  String get navigationState_cannotShowTripError;

  /// Notifications screen title
  ///
  /// In en, this message translates to:
  /// **'Notifications'**
  String get notification_title;

  /// Button text to mark all notifications as read
  ///
  /// In en, this message translates to:
  /// **'Mark all as read'**
  String get notification_markAllAsRead;

  /// Placeholder text for notification search field
  ///
  /// In en, this message translates to:
  /// **'Search notifications'**
  String get notification_searchPlaceholder;

  /// Error message when notifications fail to load
  ///
  /// In en, this message translates to:
  /// **'Error loading notifications'**
  String get notification_errorLoading;

  /// Retry button text
  ///
  /// In en, this message translates to:
  /// **'Retry'**
  String get notification_retry;

  /// Message when search returns no results
  ///
  /// In en, this message translates to:
  /// **'No notifications found'**
  String get notification_noResultsFound;

  /// Message when there are no notifications
  ///
  /// In en, this message translates to:
  /// **'No notifications'**
  String get notification_empty;

  /// Notification detail screen title
  ///
  /// In en, this message translates to:
  /// **'Notification Details'**
  String get notificationDetail_title;

  /// Section header for additional notification information
  ///
  /// In en, this message translates to:
  /// **'Additional Information'**
  String get notificationDetail_additionalInfo;

  /// Button text to view related trip
  ///
  /// In en, this message translates to:
  /// **'View Trip'**
  String get notificationDetail_viewTrip;

  /// Button text to update documents
  ///
  /// In en, this message translates to:
  /// **'Update Documents'**
  String get notificationDetail_updateDocuments;

  /// Button text to view reservation
  ///
  /// In en, this message translates to:
  /// **'View Reservation'**
  String get notificationDetail_viewReservation;

  /// Generic button text to view details
  ///
  /// In en, this message translates to:
  /// **'View Details'**
  String get notificationDetail_viewDetails;
}

class _AppLocalizationsDelegate extends LocalizationsDelegate<AppLocalizations> {
  const _AppLocalizationsDelegate();

  @override
  Future<AppLocalizations> load(Locale locale) {
    return SynchronousFuture<AppLocalizations>(lookupAppLocalizations(locale));
  }

  @override
  bool isSupported(Locale locale) => <String>['en', 'fr'].contains(locale.languageCode);

  @override
  bool shouldReload(_AppLocalizationsDelegate old) => false;
}

AppLocalizations lookupAppLocalizations(Locale locale) {


  // Lookup logic when only language code is specified.
  switch (locale.languageCode) {
    case 'en': return AppLocalizationsEn();
    case 'fr': return AppLocalizationsFr();
  }

  throw FlutterError(
    'AppLocalizations.delegate failed to load unsupported locale "$locale". This is likely '
    'an issue with the localizations generation tool. Please file an issue '
    'on GitHub with a reproducible sample app and the gen-l10n configuration '
    'that was used.'
  );
}
