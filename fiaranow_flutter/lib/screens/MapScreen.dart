import 'dart:async';

import 'package:fiaranow_flutter/screens/MapScreen/DriverTripRequestsList.dart';
import 'package:fiaranow_flutter/utils/payment_utils.dart';
import 'package:firebase_analytics/firebase_analytics.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_google_places_sdk/flutter_google_places_sdk.dart' as places;
import 'package:get/get.dart';
import 'package:google_maps_flutter/google_maps_flutter.dart';
import 'package:logging/logging.dart';

import '../firebase_cloud_functions.dart' as fcf;
import '../l10n/app_localizations.dart';
import '../models/MobileUser.dart';
import '../models/Trip.dart';
import '../models/TripStatus.dart';
import '../states/AppState.dart';
import '../states/NavigationState.dart';
import '../states/NavigationState/PreparationMixin.dart';
import '../states/PermissionsState.dart';
import 'MapScreen/DriverModeWidget.dart';
import 'MapScreen/DriverTripControl.dart';
import 'MapScreen/DriversList.dart';
import 'MapScreen/LocationPermissionPrompt.dart';
import 'MapScreen/LocationServiceDisabledOverlay.dart';
import 'MapScreen/PassengerTripControl.dart';
import 'MapScreen/PositionConfirmationButtons.dart';
import 'MapScreen/RiderModeWidget.dart';
import 'MapScreen/RouteReservationSetup.dart';
import 'MapScreen/RoutesSelectionList.dart';

class MapScreen extends StatefulWidget {
  final void Function(dynamic index) onNavigateToTab;

  const MapScreen({
    super.key,
    required this.onNavigateToTab,
  });

  @override
  State<MapScreen> createState() => _MapScreenState();
}

class _MapScreenState extends State<MapScreen> with AutomaticKeepAliveClientMixin {
  final _appState = Get.find<AppState>();
  GoogleMapController? _mapController;
  final Logger _logger = Logger('MapScreen');

  static const _madagascar = LatLng(-18.944477267648598, 46.76666647195816);
  final _navigationState = Get.find<NavigationState>();
  final _permissionsState = Get.find<PermissionsState>();

  final _oneSecondHasPassed = false.obs;

  @override
  void initState() {
    super.initState();
    _loadMapStyle();

    // Google Map doesn't load fast enough, so ...
    Timer.periodic(const Duration(milliseconds: 500), (timer) {
      if (_mapController != null) {
        timer.cancel();
        _navigationState.checkLocationPermission().then((_) {
          _afterLocationPermissionCheck();
        });
      }
    });

    Timer(const Duration(seconds: 1), () {
      _oneSecondHasPassed.value = true;
    });
  }

  Future<void> _afterLocationPermissionCheck() async {
    await _navigationState.getCurrentLocation();

    // Move the camera to the current position
    _mapController?.animateCamera(
      CameraUpdate.newCameraPosition(
        CameraPosition(
          target: _navigationState.currentPosition.value!,
          zoom: 15.0,
        ),
      ),
    );
  }

  Future<void> _getPlacePredictions(String input, bool isStartAddress) async {
    await _navigationState.getPlacePredictions(input.trim(), isStartAddress, (e) {
      Get.snackbar(
        AppLocalizations.of(context)!.mapScreen_error, // "Error"
        AppLocalizations.of(context)!
            .mapScreen_failedToGetPredictions, // "Failed to get place predictions. Please check your Internet connection."
        backgroundColor: Colors.red,
        colorText: Colors.white,
      );
    });
  }

  void _chooseStartPosition() {
    if (_navigationState.startPosition.value != null) {
      _navigationState.moveCameraToPosition(_navigationState.startPosition.value!);
    } else {
      _navigationState.isChoosingStartPosition.value = true;
      _navigationState.isChoosingDestinationPosition.value = false;
    }
    FocusScope.of(context).unfocus();
  }

  void _chooseStartPositionViaInput() {
    if (!_navigationState.isStartAddressFocused.value) {
      _navigationState.startAddressController.selection = TextSelection(
        baseOffset: 0,
        extentOffset: _navigationState.startAddressController.text.length,
      );
    }

    _navigationState.isChoosingStartPosition.value = true;
    _navigationState.isChoosingDestinationPosition.value = false;
    _navigationState.isStartAddressFocused.value = true;
    _navigationState.isDestinationAddressFocused.value = false;

    // If there is already a Start Position set in the state, move the camera to it
    if (_navigationState.startPosition.value != null) {
      _mapController?.animateCamera(
        CameraUpdate.newCameraPosition(
          CameraPosition(
            target: _navigationState.startPosition.value!,
            zoom: _navigationState.zoomLevel.value,
          ),
        ),
      );
    }
  }

  void _chooseDestinationPosition() {
    if (_navigationState.destinationPosition.value != null) {
      _navigationState.moveCameraToPosition(_navigationState.destinationPosition.value!);
    } else {
      _navigationState.isChoosingDestinationPosition.value = true;
      _navigationState.isChoosingStartPosition.value = false;
    }
    FocusScope.of(context).unfocus();
  }

  void _chooseDestinationPositionViaInput() {
    if (!_navigationState.isDestinationAddressFocused.value) {
      _navigationState.destinationAddressController.selection = TextSelection(
        baseOffset: 0,
        extentOffset: _navigationState.destinationAddressController.text.length,
      );
    }

    _navigationState.isChoosingDestinationPosition.value = true;
    _navigationState.isChoosingStartPosition.value = false;
    _navigationState.isDestinationAddressFocused.value = true;
    _navigationState.isStartAddressFocused.value = false;

    // If there is already a Destination Position set in the state, move the camera to it
    if (_navigationState.destinationPosition.value != null) {
      _mapController?.animateCamera(
        CameraUpdate.newCameraPosition(
          CameraPosition(
            target: _navigationState.destinationPosition.value!,
            zoom: _navigationState.zoomLevel.value,
          ),
        ),
      );
    }
  }

  void _clearStartPosition() {
    if (_navigationState.startPosition.value != null) {
      _mapController?.animateCamera(
        CameraUpdate.newCameraPosition(
          CameraPosition(
            target: _navigationState.startPosition.value!,
            zoom: _navigationState.zoomLevel.value,
          ),
        ),
      );
    }

    _navigationState.clearStartAddress();
    _navigationState.isChoosingDestinationPosition.value = false;
    _navigationState.isChoosingStartPosition.value = true;

    FocusScope.of(context).unfocus();
  }

  void _clearDestinationPosition() {
    if (_navigationState.destinationPosition.value != null) {
      _mapController?.animateCamera(
        CameraUpdate.newCameraPosition(
          CameraPosition(
            target: _navigationState.destinationPosition.value!,
            zoom: _navigationState.zoomLevel.value,
          ),
        ),
      );
    }

    _navigationState.clearDestinationAddress();
    _navigationState.isChoosingStartPosition.value = false;
    _navigationState.isChoosingDestinationPosition.value = true;

    FocusScope.of(context).unfocus();
  }

  void _onMapCreated(GoogleMapController controller) {
    FirebaseAnalytics.instance.logEvent(name: 'map_loaded');
    _mapController = controller;
    _navigationState.setMapController(controller);
  }

  Future<void> _loadMapStyle() async {
    _darkMapStyle = await rootBundle.loadString('assets/json/map_dark_style.json');
  }

  String? _darkMapStyle;

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
  }

  String? _tappedDriverUid;
  bool _loadingPointOfInterest = false;
  bool _loadingRouteData = false;

  double get _mapBottomPadding {
    // Rider
    if (_navigationState.drivingMode.value == UserType.rider) {
      // Check if viewing a trip
      if (_navigationState.viewingTrip.value != null && _navigationState.liveTrip == null) {
        return 240;
      }

      if (_navigationState.showNearbyDrivers.value || _navigationState.currentRiderTrip.value != null) {
        return 240;
      }

      if (_navigationState.routeOverviews.isNotEmpty && _navigationState.routeChosen.value == false) {
        return MediaQuery.of(context).size.height * 0.30;
      }

      if (_navigationState.preparationStep == PreparationSteps.reservingTrip) {
        return 300;
      }
    }

    // Driver
    else if (_navigationState.drivingMode.value == UserType.driver) {
      // Check if viewing a trip
      if (_navigationState.viewingTrip.value != null && _navigationState.liveTrip == null) {
        return 240;
      }

      if (_navigationState.driverTripRequests.isNotEmpty) {
        return 260;
      }
    }
    return 0;
  }

  Widget _buildBottomPortion() {
    final isDark = Theme.of(context).brightness == Brightness.dark;
    final backgroundColor = isDark ? Theme.of(context).cardColor : Colors.white;

    Widget content = const SizedBox.shrink();
    if (_navigationState.drivingMode.value == UserType.rider) {
      content = _buildRiderBottomContent();
    } else if (_navigationState.drivingMode.value == UserType.driver) {
      content = _buildDriverBottomContent();
    }

    return Positioned(
      bottom: 0,
      left: 0,
      right: 0,
      height: _mapBottomPadding,
      child: Container(
        decoration: BoxDecoration(
          color: backgroundColor,
          boxShadow: [
            BoxShadow(
              color: Colors.black.withValues(alpha: 0.1),
              spreadRadius: 1,
              blurRadius: 10,
            ),
          ],
        ),
        child: content,
      ),
    );
  }

  Widget _buildRiderBottomContent() {
    final currentTrip = _navigationState.currentRiderTrip.value;

    // Check if we're viewing a non-active trip
    if (_navigationState.viewingTrip.value != null && _navigationState.liveTrip == null) {
      return PassengerTripControl(mapController: _mapController);
    }

    if (currentTrip != null && currentTrip.status.index >= TripStatus.reserved.index) {
      return PassengerTripControl(mapController: _mapController);
    }

    if (_navigationState.showNearbyDrivers.value) {
      return DriversList(
        tappedDriverUid: _tappedDriverUid,
        onDriverTapped: (driverId) {
          setState(() => _tappedDriverUid = driverId);
        },
        mapController: _mapController,
      );
    }

    if (_navigationState.routeOverviews.isEmpty) {
      return const SizedBox.shrink();
    }

    if (!_navigationState.routeChosen.value) {
      return RoutesSelectionList(
        mapController: _mapController,
        onRouteSelected: _onRouteSelected,
      );
    }

    return SingleChildScrollView(
      child: RouteReservationSetup(
        onNavigateToTab: widget.onNavigateToTab,
        mapController: _mapController,
      ),
    );
  }

  Widget _buildDriverBottomContent() {
    // Check if we're viewing a non-active trip
    if (_navigationState.viewingTrip.value != null && _navigationState.liveTrip == null) {
      return DriverTripControl(mapController: _mapController);
    }

    if (_navigationState.liveTrip != null) {
      return DriverTripControl(mapController: _mapController);
    }

    if (_navigationState.driverTripRequests.isNotEmpty) {
      return DriverTripRequestsList(
        selectedRequestTripId: _navigationState.selectedRequestTripId.value,
        onTripSelected: (tripId) {
          _navigationState.selectedRequestTripId.value = tripId;
        },
        mapController: _mapController,
      );
    }

    return const SizedBox.shrink();
  }

  Widget _buildClockDriftWarning() {
    return Obx(() {
      final drift = _appState.clockDriftMillis.value.abs();

      if (drift >= 15000) {
        // More than 15 seconds drift
        return Container(
          color: Colors.black.withValues(alpha: 0.7),
          child: Center(
            child: Card(
              margin: const EdgeInsets.all(32),
              child: Padding(
                padding: const EdgeInsets.all(16),
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    const Icon(
                      Icons.warning_amber_rounded,
                      color: Colors.red,
                      size: 48,
                    ),
                    const SizedBox(height: 16),
                    Text(
                      AppLocalizations.of(context)!.mapScreen_deviceClockInaccurate, // "Your device clock is inaccurate"
                      style: Theme.of(context).textTheme.titleLarge,
                      textAlign: TextAlign.center,
                    ),
                    const SizedBox(height: 8),
                    Text(
                      AppLocalizations.of(context)!
                          .mapScreen_adjustDeviceTimeSettings, // "Please adjust your device time settings to automatic to continue using the app."
                      textAlign: TextAlign.center,
                    ),
                    Text(
                      AppLocalizations.of(context)!.mapScreen_currentTimeDifference(
                        // "Current time difference: {seconds} seconds"
                        (drift / 1000).toStringAsFixed(1),
                      ),
                      style: Theme.of(context).textTheme.bodySmall,
                      textAlign: TextAlign.center,
                    ),
                    const SizedBox(height: 16),
                    ElevatedButton(
                      onPressed: () {
                        _appState.clockDriftMillis.value = 0;
                      },
                      child: Text(
                        AppLocalizations.of(context)!.mapScreen_dismissClockWarning, // "Dismiss"
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ),
        );
      } else if (drift >= 5000) {
        // 5-15 seconds drift - show dismissable warning
        return Positioned(
          top: MediaQuery.of(context).padding.top + 16,
          left: 16,
          right: 16,
          child: Material(
            elevation: 4,
            borderRadius: BorderRadius.circular(8),
            child: Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: Colors.orange.shade100,
                borderRadius: BorderRadius.circular(8),
              ),
              child: Row(
                children: [
                  const Icon(Icons.warning_amber_rounded, color: Colors.orange),
                  const SizedBox(width: 12),
                  Expanded(
                    child: Text(
                      AppLocalizations.of(context)!.mapScreen_deviceClockOff(
                        // "Your device clock is off by {seconds} seconds. Consider enabling automatic time settings."
                        (drift / 1000).toStringAsFixed(1),
                      ),
                      style: const TextStyle(color: Colors.black87),
                    ),
                  ),
                  IconButton(
                    icon: const Icon(Icons.close),
                    onPressed: () {
                      _appState.clockDriftMillis.value = 0;
                    },
                  ),
                ],
              ),
            ),
          ),
        );
      }

      return const SizedBox.shrink();
    });
  }

  //
  // ███    ███  █████  ██ ███    ██     ██████  ██    ██ ██ ██      ██████
  // ████  ████ ██   ██ ██ ████   ██     ██   ██ ██    ██ ██ ██      ██   ██
  // ██ ████ ██ ███████ ██ ██ ██  ██     ██████  ██    ██ ██ ██      ██   ██
  // ██  ██  ██ ██   ██ ██ ██  ██ ██     ██   ██ ██    ██ ██ ██      ██   ██
  // ██      ██ ██   ██ ██ ██   ████     ██████   ██████  ██ ███████ ██████
  //
  @override
  Widget build(BuildContext context) {
    super.build(context);

    return Obx(
      () => Stack(
        children: [
          // ================================
          //
          // ╔═╗╔═╗╔═╗╔═╗╦  ╔═╗  ╔╦╗╔═╗╔═╗╔═╗
          // ║ ╦║ ║║ ║║ ╦║  ║╣   ║║║╠═╣╠═╝╚═╗
          // ╚═╝╚═╝╚═╝╚═╝╩═╝╚═╝  ╩ ╩╩ ╩╩  ╚═╝
          //
          // ================================
          Positioned(
            top: 0,
            left: 0,
            right: 0,
            bottom: _mapBottomPadding,
            child: !_permissionsState.hasLocationPermission(background: false)
                ? Container(
                    color: Colors.grey[300],
                  )
                : GoogleMap(
                    onMapCreated: _onMapCreated,
                    initialCameraPosition: const CameraPosition(
                      target: _madagascar,
                      zoom: 5.7083,
                    ),
                    style: Theme.of(context).brightness == Brightness.dark ? _darkMapStyle : null,
                    onCameraMove: (CameraPosition position) {
                      // Warning: No expensive operations here
                      _navigationState.zoomLevel.value = position.zoom;
                      _navigationState.currentMapPosition.value = position.target;
                    },
                    onPointOfInterestTap: (pointOfInterest) async {
                      // We only care about POI taps when choosing start or destination positions
                      if (_navigationState.liveTrip == null && _navigationState.isChoosingStartPosition.value ||
                          _navigationState.isChoosingDestinationPosition.value) {
                        _navigationState.setFollowingPosition(false);

                        final placeId = pointOfInterest.value;

                        setState(() {
                          _loadingPointOfInterest = true;
                        });

                        try {
                          // Set the name of the place as the address
                          final details = await _navigationState.getPlaceDetails(placeId);
                          if (details == null) return;
                          if (_navigationState.isChoosingStartPosition.value) {
                            _navigationState.setStartPosition(LatLng(details['lat'], details['lon']));
                            _navigationState.startAddress.value = details['name'];
                            _navigationState.startAddressController.text = details['name'];
                          } else {
                            _navigationState.setDestinationPosition(LatLng(details['lat'], details['lon']));
                            _navigationState.destinationAddress.value = details['name'];
                            _navigationState.destinationAddressController.text = details['name'];
                          }

                          _mapController?.animateCamera(
                            CameraUpdate.newCameraPosition(
                              CameraPosition(
                                target: LatLng(details['lat'], details['lon']),
                                zoom: _navigationState.zoomLevel.value,
                              ),
                            ),
                          );
                          _navigationState.currentMapPosition.value = LatLng(details['lat'], details['lon']);
                        } catch (e) {
                          if (mounted) {
                            Get.snackbar(
                              AppLocalizations.of(Get.context!)!.mapScreen_error, // "Error"
                              AppLocalizations.of(Get.context!)!
                                  .mapScreen_failedToGetPlaceDetails, // "Failed to get place details. Please check your Internet connection."
                              backgroundColor: Colors.red,
                              colorText: Colors.white,
                            );
                          }
                        } finally {
                          setState(() {
                            _loadingPointOfInterest = false;
                          });
                        }
                      }
                    },
                    compassEnabled: true,
                    myLocationEnabled: true,
                    myLocationButtonEnabled: _navigationState.drivingMode.value == UserType.rider,
                    trafficEnabled: true,
                    zoomControlsEnabled: true,
                    zoomGesturesEnabled: true,
                    circles: {
                      if (_navigationState.showNearbyDrivers.value)
                        Circle(
                          circleId: const CircleId('nearbyDriversRadius'),
                          center: _navigationState.startPosition.value!,
                          radius: _appState.tripConfiguration.value.nearbyDriverListedRadiusMeters,
                          fillColor: Colors.blue.withValues(alpha: 0.1),
                          strokeColor: Colors.blue,
                          strokeWidth: 1,
                        ),
                    },
                    markers: {
                      if (_navigationState.startPosition.value != null &&
                          (_navigationState.isChoosingStartPosition.value == false || _navigationState.displayTrip != null))
                        Marker(
                          markerId: const MarkerId('start'),
                          position: _navigationState.startPosition.value!,
                          icon: BitmapDescriptor.defaultMarkerWithHue(BitmapDescriptor.hueBlue),
                          infoWindow: const InfoWindow(title: 'Departure'),
                        ),
                      if (_navigationState.destinationPosition.value != null &&
                          (_navigationState.isChoosingDestinationPosition.value == false || _navigationState.displayTrip != null))
                        Marker(
                          markerId: const MarkerId('destination'),
                          position: _navigationState.destinationPosition.value!,
                          icon: BitmapDescriptor.defaultMarkerWithHue(BitmapDescriptor.hueGreen),
                          infoWindow: const InfoWindow(title: 'Destination'),
                        ),
                      if (_navigationState.showNearbyDrivers.value)
                        ..._navigationState.nearbyDrivers
                            .where((driver) =>
                                _navigationState.liveTrip?.driver == null ||
                                (_navigationState.liveTrip!.driver!['lat'] == null ||
                                    _navigationState.liveTrip!.driver!['lon'] == null) ||
                                driver.latitude != _navigationState.liveTrip!.driver!['lat'] ||
                                driver.longitude != _navigationState.liveTrip!.driver!['lon'])
                            .map((driver) => Marker(
                                  markerId: MarkerId(driver.hashCode.toString()),
                                  position: LatLng(driver.latitude, driver.longitude),
                                  icon: BitmapDescriptor.defaultMarkerWithHue(BitmapDescriptor.hueYellow),
                                )),
                      if (_navigationState.drivingMode.value == UserType.rider &&
                          _navigationState.liveTrip != null &&
                          _navigationState.driverLatestPosition.value != null)
                        Marker(
                          markerId: const MarkerId('driver'),
                          position: _navigationState.driverLatestPosition.value!,
                          icon: BitmapDescriptor.defaultMarkerWithHue(BitmapDescriptor.hueViolet),
                          infoWindow: const InfoWindow(title: 'Driver Position'),
                        ),
                      if (_navigationState.selectedRequestTripId.value != null)
                        ...(_navigationState.driverTripRequests
                            .where((trip) => trip.id == _navigationState.selectedRequestTripId.value)
                            .map((trip) => [
                                  if (trip.startLocation != null)
                                    Marker(
                                      markerId: const MarkerId('tripStart'),
                                      position: LatLng(trip.startLocation!.lat, trip.startLocation!.lon),
                                      infoWindow: const InfoWindow(title: 'Start Position'),
                                      icon: BitmapDescriptor.defaultMarkerWithHue(BitmapDescriptor.hueBlue),
                                    ),
                                  if (trip.arrivalLocation != null && trip.reservationType != ReservationType.fullDay)
                                    Marker(
                                      markerId: const MarkerId('tripDestination'),
                                      position: LatLng(trip.arrivalLocation!.lat, trip.arrivalLocation!.lon),
                                      infoWindow: const InfoWindow(title: 'Destination'),
                                      icon: BitmapDescriptor.defaultMarkerWithHue(BitmapDescriptor.hueGreen),
                                    ),
                                ])
                            .expand((markers) => markers)),
                    },
                    polylines: {
                      // For current rider trip
                      if (_navigationState.currentRiderTrip.value != null &&
                          _navigationState.currentRiderTrip.value!.routeData != null &&
                          _navigationState.currentRiderTrip.value!.reservationType != ReservationType.fullDay)
                        Polyline(
                          polylineId: const PolylineId('route'),
                          points: _navigationState.currentRiderTrip.value!.routeData!.mapsPolylines,
                          color: Colors.blue,
                          width: 5,
                        ),
                      // For display trip (could be active or viewing)
                      if (_navigationState.displayTrip != null) ...[
                        if (_navigationState.displayTrip!.routeData != null &&
                            _navigationState.displayTrip!.reservationType != ReservationType.fullDay)
                          Polyline(
                            polylineId: const PolylineId('displayTripRoute'),
                            points: _navigationState.displayTrip!.routeData!.mapsPolylines,
                            color: Colors.blue,
                            width: 5,
                          ),
                        if (_navigationState.displayTrip!.driverRouteData != null &&
                            _navigationState.displayTrip!.reservationType != ReservationType.fullDay)
                          Polyline(
                            polylineId: const PolylineId('displayTripDriverRoute'),
                            points: _navigationState.displayTrip!.driverRouteData!.mapsPolylines,
                            color: Colors.orange,
                            width: 5,
                          ),
                      ],
                      if (_navigationState.drivingMode.value == UserType.driver &&
                          _navigationState.selectedRequestTripId.value != null)
                        ..._navigationState.driverTripRequests
                            .where((trip) =>
                                trip.id == _navigationState.selectedRequestTripId.value &&
                                trip.reservationType != ReservationType.fullDay)
                            .map((trip) {
                              // Get route data from cache or embedded data
                              final routeData = _navigationState.getCachedRouteDataSync(trip);
                              if (routeData == null) {
                                // Route data not loaded yet, trigger async load
                                _navigationState.getCachedRouteData(trip).then((_) {
                                  // Trigger rebuild when route data is loaded
                                  if (mounted) setState(() {});
                                });
                                return null;
                              }
                              return Polyline(
                                polylineId: PolylineId('trip_${trip.id}'),
                                points: routeData.mapsPolylines,
                                color: Colors.blue,
                                width: 5,
                              );
                            })
                            .where((polyline) => polyline != null)
                            .cast<Polyline>(),
                      if (_navigationState.drivingMode.value == UserType.driver &&
                          _navigationState.selectedRequestTripId.value != null)
                        ..._navigationState.driverTripRequests
                            .where((trip) =>
                                trip.id == _navigationState.selectedRequestTripId.value &&
                                trip.driverRouteData != null &&
                                trip.reservationType != ReservationType.fullDay)
                            .map((trip) => Polyline(
                                  polylineId: PolylineId('driverToStart_${trip.id}'),
                                  points: trip.driverRouteData!.mapsPolylines,
                                  color: Colors.orange,
                                  width: 5,
                                )),
                      if (_navigationState.routeOverviews.isNotEmpty)
                        Polyline(
                          polylineId: const PolylineId('overview'),
                          points: _navigationState.routeOverviews[_navigationState.selectedRouteIndex.value].mapsPolylines,
                          color: Colors.blue,
                          width: 5,
                        ),
                    },
                  ),
          ),

          _buildBottomPortion(),

          // Passenger: Show confirmation buttons after choosing start or destination positions - hide when viewing a trip
          if (_navigationState.drivingMode.value == UserType.rider &&
              _navigationState.currentRiderTrip.value == null &&
              _navigationState.viewingTrip.value == null &&
              (_navigationState.isChoosingStartPosition.value || _navigationState.isChoosingDestinationPosition.value))
            PositionConfirmationButtons(
              mapBottomPadding: _mapBottomPadding,
              onConfirmBothPositions: _getRoutesThenZoomMapToFitPositions,
              mapController: _mapController,
            ),

          // UIs on top of the map - hide when viewing a trip
          _navigationState.drivingMode.value == UserType.rider && _navigationState.viewingTrip.value == null
              ? RiderModeWidget(
                  isChoosingStartPosition: _navigationState.isChoosingStartPosition.value,
                  isChoosingDestinationPosition: _navigationState.isChoosingDestinationPosition.value,
                  onGetPlacePredictions: _getPlacePredictions,
                  onChooseStartPosition: _chooseStartPosition,
                  onChooseStartPositionViaInput: _chooseStartPositionViaInput,
                  onChooseDestinationPosition: _chooseDestinationPosition,
                  onChooseDestinationPositionViaInput: _chooseDestinationPositionViaInput,
                  onClearStartPosition: _clearStartPosition,
                  onClearDestinationPosition: _clearDestinationPosition,
                  buildPredictionList: _buildPredictionList,
                )
              : _navigationState.drivingMode.value == UserType.driver
                  ? DriverModeWidget()
                  : const SizedBox.shrink(),

          // Loading indicator ...
          if (_loadingPointOfInterest || _loadingRouteData)
            Container(
              color: Colors.black.withValues(alpha: 0.3),
              child: const Center(
                child: CircularProgressIndicator(),
              ),
            ),

          // Display location service disabled overlay - highest priority
          if (_oneSecondHasPassed.value)
            Obx(() {
              if (!_permissionsState.locationServiceEnabled.value) {
                return LocationServiceDisabledOverlay();
              }
              return const SizedBox.shrink();
            }),
          // Display the location permission prompt (not immediately, wait for a second)
          if (_oneSecondHasPassed.value)
            Obx(() {
              if (_permissionsState.locationServiceEnabled.value && 
                  !_permissionsState.hasLocationPermission(background: false)) {
                return LocationPermissionPrompt(
                  onAfterPermissionCheck: _afterLocationPermissionCheck,
                );
              }
              return const SizedBox.shrink();
            }),

          // Display clock drift warning
          _buildClockDriftWarning(),

          // Show loading overlay if not fully initialized AND the 1-second delay has passed
          // This ensures we don't show loading when we're just waiting to check permissions
          Obx(() {
            if (!_navigationState.isFullyInitialized.value && _oneSecondHasPassed.value) {
              return Container(
              color: Theme.of(context).scaffoldBackgroundColor,
              child: SafeArea(
                child: Center(
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      CircularProgressIndicator(
                        valueColor: AlwaysStoppedAnimation<Color>(
                          Theme.of(context).primaryColor,
                        ),
                      ),
                      const SizedBox(height: 24),
                      Text(
                        AppLocalizations.of(context)!.mapScreen_loadingMap, // "Loading map..."
                        style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                              color: Colors.grey[600],
                            ),
                      ),
                    ],
                  ),
                ),
              ),
            );
            }
            return const SizedBox.shrink();
          }),
        ],
      ),
    );
  }

  Widget _buildPredictionList(RxList<places.AutocompletePrediction> predictions, bool isStartAddress) {
    if (predictions.isEmpty) return const SizedBox.shrink();

    final isDark = Theme.of(context).brightness == Brightness.dark;
    final backgroundColor = isDark ? Theme.of(context).cardColor : Colors.white;
    final textColor = isDark ? Colors.white : Colors.black;

    return Container(
      margin: const EdgeInsets.only(top: 4.0),
      decoration: BoxDecoration(
        color: backgroundColor,
        border: Border.all(color: Theme.of(context).primaryColor, width: 1.0),
        borderRadius: BorderRadius.circular(4.0),
      ),
      child: ListView.separated(
        shrinkWrap: true,
        itemCount: predictions.length,
        separatorBuilder: (context, index) => Divider(
          height: 1.0,
          color: isDark ? Colors.grey[700] : Colors.grey[300],
        ),
        itemBuilder: (context, index) {
          return ListTile(
            title: Text(
              predictions[index].fullText,
              style: TextStyle(color: textColor),
            ),
            onTap: () async {
              final addressPosition = await _navigationState.handlePredictionPlaceSelect(predictions[index], isStartAddress);
              await _navigationState.moveCameraToPosition(addressPosition, zoomLevel: 17.5);
              _navigationState.currentMapPosition.value = addressPosition;

              if (context.mounted) {
                FocusScope.of(context).unfocus();
              }
            },
          );
        },
      ),
    );
  }

  void _onShowNearbyDriversPressed() async {
    // Toggle (reverse)
    if (_navigationState.showNearbyDrivers.value) {
      // Show the address input fields
      setState(() {
        _navigationState.showNearbyDrivers.value = false;
        _navigationState.stopListeningNearbyDrivers();
      });

      return;
    }

    // Close the keyboard and show the nearby drivers
    setState(() {
      _navigationState.isStartAddressFocused.value = false;
      _navigationState.isDestinationAddressFocused.value = false;
      _navigationState.showNearbyDrivers.value = true;
      _navigationState.setFollowingPosition(false);
    });
    FocusScope.of(context).unfocus();

    // Zoom the map to show the Start position only
    _zoomMapToStartPosition();

    // Fetch nearby drivers
    await _navigationState.startListeningForNearbyDrivers(_navigationState.startPosition.value!);

    _forceCloseKeyboard();
  }

  void _forceCloseKeyboard() async {
    for (int i = 0; i < 20; i++) {
      await Future.delayed(const Duration(milliseconds: 50));
      if (mounted) {
        FocusScope.of(context).unfocus();
      }
    }
  }

  void _zoomMapToStartPosition() {
    _mapController?.animateCamera(CameraUpdate.newLatLngZoom(
      _navigationState.startPosition.value!,
      _navigationState.zoomLevel.value,
    ));
  }

  Future<void> _getRoutesThenZoomMapToFitPositions() async {
    final startPosition = _navigationState.startPosition.value!;
    final destinationPosition = _navigationState.destinationPosition.value!;

    FirebaseAnalytics.instance.logEvent(
      name: 'route_calculated',
      parameters: {
        'start_lat': startPosition.latitude,
        'start_lng': startPosition.longitude,
        'destination_lat': destinationPosition.latitude,
        'destination_lng': destinationPosition.longitude,
      },
    );

    late List<RouteData> routesData;
    try {
      setState(() {
        _loadingRouteData = true;
        _navigationState.setRouteChosen(false);
      });

      routesData = await fcf.getMultipleRoutesData(
        fcf.LatLng(lat: startPosition.latitude, lon: startPosition.longitude),
        fcf.LatLng(lat: destinationPosition.latitude, lon: destinationPosition.longitude),
        highQuality: true,
      );
    } catch (e, stackTrace) {
      _logger.severe('❌ Error calculating routes', e, stackTrace);
      _logger.fine('Stack trace: $stackTrace');

      if (mounted) {
        Get.snackbar(
          AppLocalizations.of(context)!.mapScreen_error,
          AppLocalizations.of(context)!.mapScreen_failedToGetRoutes,
          backgroundColor: Colors.red,
          colorText: Colors.white,
        );
      }
      return;
    } finally {
      setState(() {
        _loadingRouteData = false;
      });
    }

    final firstBounds = routesData.first.bounds;

    await _mapController?.animateCamera(
      CameraUpdate.newLatLngBounds(firstBounds, 50),
    );

    _navigationState.selectedRouteIndex.value = 0;
    _navigationState.routeOverviews.assignAll(routesData.cast<RouteData>());
  }

  void _onRouteSelected() async {
    // First show passenger count dialog
    final passengerCount = await showPassengerCountSelectionDialog(context, analyticsSource: 'map_screen');

    if (passengerCount != null) {
      // Explicitly set the passenger count to ensure it's updated
      _navigationState.passengerCount.value = passengerCount;

      // Only proceed to payment method if passenger count was selected
      _showPaymentMethodDialog();
    }
  }

  void _showPaymentMethodDialog() async {
    final paymentMethod = await showPaymentMethodSelectionDialog(context, analyticsSource: 'map_screen');

    if (paymentMethod != null) {
      _showRideTypeDialog(paymentMethod);
    }
  }

  void _showRideTypeDialog(String paymentMethod) async {
    final choice = await Get.dialog<String>(
      AlertDialog(
        title: Text(AppLocalizations.of(context)!.mapScreen_chooseRideType), // "Choose Ride Type"
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            ListTile(
              leading: const Icon(Icons.directions_car),
              title: Text(AppLocalizations.of(context)!.mapScreen_rideNow), // "Ride now"
              subtitle: Text(AppLocalizations.of(context)!.mapScreen_findAvailableDrivers), // "Find available drivers"
              onTap: () {
                Get.back(result: 'now');
              },
            ),
            const Divider(),
            ListTile(
              leading: const Icon(Icons.timer),
              title: Text(AppLocalizations.of(context)!.mapScreen_reserveRide), // "Reserve a ride"
              subtitle: Text(AppLocalizations.of(context)!.mapScreen_chooseDateAndTime), // "Choose date and time"
              onTap: () {
                Get.back(result: 'reserve');
              },
            ),
          ],
        ),
      ),
      barrierDismissible: true,
    );

    if (choice == 'now') {
      _appState.userTendency.value = UserTendency.rideNow;
      _navigationState.currentRequestedPaymentMethod = paymentMethod;
      _onShowNearbyDriversPressed();
    } else if (choice == 'reserve') {
      _appState.userTendency.value = UserTendency.reserve;
      _navigationState.currentRequestedPaymentMethod = paymentMethod;
      _navigationState.setRouteChosen(true);
    }
  }

  @override
  bool get wantKeepAlive => true;
}
