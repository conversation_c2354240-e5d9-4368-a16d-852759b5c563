import 'package:fiaranow_flutter/l10n/app_localizations.dart';
import 'package:fiaranow_flutter/states/PermissionsState.dart';
import 'package:firebase_analytics/firebase_analytics.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';

class LocationServiceDisabledOverlay extends StatelessWidget {
  LocationServiceDisabledOverlay({super.key}); // Not const, per flutter.md guidelines

  final permissionsState = Get.find<PermissionsState>();

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final isDark = theme.brightness == Brightness.dark;
    
    return Container(
      color: Colors.black.withValues(alpha: 0.9), // More opaque than permission prompt
      child: Center(
        child: Container(
          margin: const EdgeInsets.symmetric(horizontal: 16.0),
          padding: const EdgeInsets.all(24.0),
          decoration: BoxDecoration(
            color: isDark ? theme.cardColor : Colors.white,
            borderRadius: BorderRadius.circular(12.0),
            border: Border.all(color: Colors.red, width: 2),
          ),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              const Icon(Icons.location_disabled, size: 64, color: Colors.red),
              const SizedBox(height: 20.0),
              Text(
                AppLocalizations.of(context)!.mapScreen_locationServiceDisabledTitle,
                style: const TextStyle(
                  fontSize: 20,
                  fontWeight: FontWeight.bold,
                  color: Colors.red,
                ),
                textAlign: TextAlign.center,
              ),
              const SizedBox(height: 12.0),
              Text(
                AppLocalizations.of(context)!.mapScreen_locationServiceDisabledMessage,
                textAlign: TextAlign.center,
                style: TextStyle(
                  fontSize: 16,
                  color: isDark ? Colors.white70 : Colors.black87,
                ),
              ),
              const SizedBox(height: 24.0),
              ElevatedButton.icon(
                onPressed: () async {
                  await permissionsState.openLocationSettings();
                  FirebaseAnalytics.instance.logEvent(
                    name: 'location_settings_opened',
                    parameters: {
                      'reason': 'service_disabled_overlay',
                      'widget_name': 'location_service_disabled_overlay'
                    },
                  );
                },
                icon: const Icon(Icons.settings),
                label: Text(AppLocalizations.of(context)!.mapScreen_openLocationSettingsButton),
                style: ElevatedButton.styleFrom(
                  backgroundColor: Colors.red,
                  foregroundColor: Colors.white,
                  padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}