import 'dart:async';
import 'dart:io';

import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:device_info_plus/device_info_plus.dart';
import 'package:firebase_messaging/firebase_messaging.dart';
import 'package:flutter/foundation.dart';
import 'package:get/get.dart';
import 'package:logging/logging.dart';

import '../fcm.dart';
import '../states/AuthState.dart' as fiaranow;
import 'FCMErrorMonitor.dart';

class FCMTokenService extends GetxService {
  static FCMTokenService get instance => Get.find();
  
  final Logger _logger = Logger('FCMTokenService');
  
  // Token refresh listener
  StreamSubscription<String>? _tokenRefreshSubscription;
  
  // Retry timer for failed token updates
  Timer? _retryTimer;
  
  // Current token cache
  String? _currentToken;
  
  // iOS-specific state tracking
  bool _isIOSSimulator = false;
  
  @override
  void onInit() {
    super.onInit();
    _logger.info('Initializing FCMTokenService');
    _setupTokenRefreshListener();
    
    // Run startup validation for iOS
    if (Platform.isIOS) {
      _validateIOSConfiguration();
    }
  }
  
  /// iOS environment detection and validation
  Future<void> _validateIOSConfiguration() async {
    try {
      // Proper iOS simulator detection using device info
      if (Platform.isIOS) {
        try {
          final DeviceInfoPlugin deviceInfo = DeviceInfoPlugin();
          final IosDeviceInfo iosInfo = await deviceInfo.iosInfo;
          _isIOSSimulator = !iosInfo.isPhysicalDevice;
        } catch (e) {
          // Fallback detection
          _isIOSSimulator = !kReleaseMode;
          _logger.warning('iOS: Fallback simulator detection used: $e');
        }
      }
      
      if (_isIOSSimulator) {
        _logger.warning('⚠️ iOS: Running on simulator - FCM tokens NOT supported');
        _logger.warning('⚠️ iOS: Push notifications require a physical device');
      } else {
        _logger.info('✅ iOS: Running on physical device - FCM tokens supported');
      }
      
      FCMErrorMonitor.reportSuccess('ios_config_validation');
      
    } catch (e) {
      _logger.severe('iOS: Configuration validation failed: $e');
      FCMErrorMonitor.reportError('ios_config_validation', e, null);
    }
  }
  
  
  @override
  void onClose() {
    _tokenRefreshSubscription?.cancel();
    _retryTimer?.cancel();
    super.onClose();
  }
  
  /// Set up the token refresh listener to handle iOS token changes
  void _setupTokenRefreshListener() {
    _tokenRefreshSubscription = FirebaseMessaging.instance.onTokenRefresh.listen(
      (token) async {
        _logger.info('🔄 FCM token refreshed: $token');
        
        // For iOS, validate APNS token is still available during refresh
        if (Platform.isIOS) {
          await _validateAPNSTokenOnRefresh(token);
        } else {
          // Android - direct update
          _currentToken = token;
          await _updateTokenInFirestore(token);
          FCMErrorMonitor.reportSuccess('token_refresh');
        }
      },
      onError: (error) {
        _logger.severe('❌ FCM token refresh error: $error');
        FCMErrorMonitor.reportError('token_refresh', error, null);
        
        // For iOS APNS token errors during refresh, trigger re-setup
        if (Platform.isIOS && error.toString().contains('apns-token-not-set')) {
          _logger.warning('🔄 iOS: APNS token error during refresh, triggering re-setup');
          _handleAPNSTokenError();
        }
      },
    );
    
    _logger.info('✅ FCM token refresh listener set up');
  }
  
  /// Validate APNS token availability during iOS token refresh
  Future<void> _validateAPNSTokenOnRefresh(String newToken) async {
    try {
      final apnsToken = await FirebaseMessaging.instance.getAPNSToken();
      
      if (apnsToken != null) {
        _logger.info('✅ iOS: APNS token valid during refresh, updating FCM token');
        _currentToken = newToken;
        await _updateTokenInFirestore(newToken);
        FCMErrorMonitor.reportSuccess('token_refresh_ios');
      } else {
        _logger.warning('⚠️ iOS: APNS token null during refresh, will retry');
        // Try to re-establish APNS token
        await _handleAPNSTokenError();
      }
    } catch (e) {
      _logger.severe('❌ iOS: Error validating APNS token during refresh: $e');
      FCMErrorMonitor.reportError('token_refresh_apns_validation', e, null);
      await _handleAPNSTokenError();
    }
  }
  
  /// Handle APNS token errors by triggering a fresh token setup
  Future<void> _handleAPNSTokenError() async {
    try {
      _logger.info('🔄 iOS: Handling APNS token error, requesting fresh setup');
      
      // Wait a moment for iOS to stabilize
      await Future.delayed(const Duration(seconds: 2));
      
      // Request fresh token setup
      await setTokenForCurrentUser();
      
    } catch (e) {
      _logger.severe('❌ iOS: Error handling APNS token error: $e');
      FCMErrorMonitor.reportError('handle_apns_error', e, null);
    }
  }
  
  /// Get FCM token for current user and update Firestore
  Future<void> setTokenForCurrentUser() async {
    final authState = Get.find<fiaranow.AuthState>();
    final uid = authState.uid;
    
    if (uid.isEmpty) {
      _logger.warning('⚠️ Cannot set FCM token: user not authenticated');
      return;
    }
    
    // Skip FCM token operations on iOS Simulator
    if (Platform.isIOS && _isIOSSimulator) {
      _logger.warning('⚠️ iOS Simulator detected - skipping FCM token operations');
      _logger.info('📱 Use a physical iOS device to test push notifications');
      return;
    }
    
    await _setFCMTokenWithRetry(uid);
  }
  
  /// Set FCM token with retry mechanism and proper iOS APNS handling
  Future<void> _setFCMTokenWithRetry(String uid) async {
    const int maxRetries = 5;
    const Duration retryDelay = Duration(seconds: 3);

    for (int attempt = 1; attempt <= maxRetries; attempt++) {
      try {
        _logger.info('🔑 FCM Token attempt $attempt/$maxRetries');

        String? token;
        if (Platform.isIOS) {
          // For iOS, ensure APNS token is available first
          token = await _getIOSFCMTokenWithAPNSCheck(attempt);
        } else {
          // For Android and other platforms
          token = await FirebaseMessaging.instance.getToken();
        }

        if (token?.isNotEmpty ?? false) {
          await _updateTokenInFirestore(token!, uid: uid);
          _logger.info('✅ FCM token successfully set on attempt $attempt: $token');
          _currentToken = token;
          
          // Cancel any pending retry timer since we succeeded
          _retryTimer?.cancel();
          _retryTimer = null;
          
          FCMErrorMonitor.reportSuccess('set_fcm_token');
          return;
        } else {
          _logger.warning('⚠️ FCM token is null on attempt $attempt');
        }
      } catch (e) {
        _logger.severe('❌ FCM token setting failed on attempt $attempt: $e');
        FCMErrorMonitor.reportError('set_fcm_token', e, null);
        
        // Special handling for iOS APNS token errors
        if (Platform.isIOS && e.toString().contains('apns-token-not-set')) {
          _logger.warning('⚠️ iOS APNS token not available, will retry with extended delay');
          if (attempt < maxRetries) {
            await Future.delayed(Duration(seconds: retryDelay.inSeconds * 2)); // Extended delay for iOS
            continue;
          }
        }
      }

      // Wait before retry (except on last attempt)
      if (attempt < maxRetries) {
        _logger.info('⏳ Retrying FCM token in ${retryDelay.inSeconds} seconds...');
        await Future.delayed(retryDelay);
      }
    }

    _logger.severe('❌ FCM token setting failed after $maxRetries attempts');
    FCMErrorMonitor.reportError('set_fcm_token_final_failure', 'Failed after $maxRetries attempts', null);

    // Start a periodic retry timer for cases where token becomes available later
    _startPeriodicRetry(uid);
  }
  
  /// iOS-specific FCM token retrieval with APNS token validation
  Future<String?> _getIOSFCMTokenWithAPNSCheck(int attempt) async {
    try {
      // For iOS simulator, return null as FCM tokens are not supported
      if (_isIOSSimulator) {
        _logger.warning('iOS: Simulator does not support FCM tokens - returning null');
        _logger.info('📱 Test push notifications on a physical iOS device');
        return null;
      }
      
      // For physical devices, wait for APNS token with short delay
      String? apnsToken = await _waitForAPNSToken();
      
      if (apnsToken != null) {
        // APNS token available, get FCM token
        return await FirebaseMessaging.instance.getToken();
      } else {
        // APNS token not available, but try FCM anyway (iOS might have registered in background)
        _logger.warning('iOS: APNS token null, trying FCM anyway (attempt $attempt)');
        return await FirebaseMessaging.instance.getToken();
      }
    } catch (e) {
      _logger.severe('iOS: FCM token error: $e');
      FCMErrorMonitor.reportError('ios_fcm_token', e, null);
      return null;
    }
  }
  
  
  /// Wait for APNS token with simple delay - based on Firebase docs pattern
  Future<String?> _waitForAPNSToken() async {
    // First immediate check
    String? apnsToken = await FirebaseMessaging.instance.getAPNSToken();
    if (apnsToken != null) return apnsToken;
    
    // Wait 3 seconds as recommended in Firebase docs, then try again
    await Future.delayed(const Duration(seconds: 3));
    apnsToken = await FirebaseMessaging.instance.getAPNSToken();
    
    return apnsToken; // Return null if still not available
  }
  
  /// Start periodic retry for FCM token (useful for iOS simulator and edge cases)
  void _startPeriodicRetry(String uid) {
    _retryTimer?.cancel();
    _retryTimer = Timer.periodic(const Duration(minutes: 2), (timer) async {
      _logger.info('🔄 Periodic FCM token retry');

      try {
        // Check if token is already set
        final userDoc = await FirebaseFirestore.instance.collection('mobile_users').doc(uid).get();
        final currentToken = userDoc.data()?['fcmToken'] as String?;

        if (currentToken != null && currentToken.isNotEmpty) {
          _logger.info('✅ FCM token already exists, stopping periodic retry');
          timer.cancel();
          return;
        }

        // Try to get token again
        String? token;
        if (defaultTargetPlatform == TargetPlatform.iOS) {
          token = await getFCMToken();
          token ??= await FirebaseMessaging.instance.getToken();
        } else {
          token = await FirebaseMessaging.instance.getToken();
        }

        if (token != null && token.isNotEmpty) {
          await _updateTokenInFirestore(token, uid: uid);
          _logger.info('✅ FCM token set via periodic retry: $token');
          _currentToken = token;
          FCMErrorMonitor.reportSuccess('periodic_retry_fcm_token');
          timer.cancel();
        }
      } catch (e) {
        _logger.severe('❌ Periodic FCM token retry failed: $e');
        FCMErrorMonitor.reportError('periodic_retry_fcm_token', e, null);
      }
    });
  }
  
  /// Update FCM token in Firestore
  Future<void> _updateTokenInFirestore(String token, {String? uid}) async {
    try {
      final targetUid = uid ?? Get.find<fiaranow.AuthState>().uid;
      
      if (targetUid.isEmpty) {
        _logger.warning('⚠️ Cannot update FCM token: user not authenticated');
        return;
      }
      
      await FirebaseFirestore.instance.collection('mobile_users').doc(targetUid).update({
        'fcmToken': token,
        'fcmTokenUpdatedAt': FieldValue.serverTimestamp(),
      });
      
      _logger.info('✅ FCM token updated in Firestore for user: $targetUid');
      FCMErrorMonitor.reportSuccess('update_token_firestore');
    } catch (e) {
      _logger.severe('❌ Failed to update FCM token in Firestore: $e');
      FCMErrorMonitor.reportError('update_token_firestore', e, null);
      rethrow;
    }
  }
  
  /// Get current cached token
  String? get currentToken => _currentToken;
  
  /// Get iOS APNS token status for debugging
  Future<Map<String, dynamic>> getIOSDebugInfo() async {
    if (!Platform.isIOS) {
      return {'platform': 'not_ios', 'error': 'This method is only available on iOS'};
    }
    
    try {
      final apnsToken = await FirebaseMessaging.instance.getAPNSToken();
      final notificationSettings = await FirebaseMessaging.instance.getNotificationSettings();
      
      return {
        'platform': 'ios',
        'apns_token_available': apnsToken != null,
        'apns_token_length': apnsToken?.length ?? 0,
        'notification_status': notificationSettings.authorizationStatus.toString(),
        'fcm_token_cached': _currentToken != null,
        'fcm_token_length': _currentToken?.length ?? 0,
        'timestamp': DateTime.now().toIso8601String(),
      };
    } catch (e) {
      return {
        'platform': 'ios',
        'error': e.toString(),
        'timestamp': DateTime.now().toIso8601String(),
      };
    }
  }
  
  /// Force refresh token and update Firestore
  Future<void> refreshToken() async {
    try {
      _logger.info('🔄 Force refreshing FCM token');
      await FirebaseMessaging.instance.deleteToken();
      
      String? newToken;
      if (Platform.isIOS) {
        // For iOS, use the robust APNS-aware method
        newToken = await _getIOSFCMTokenWithAPNSCheck(1);
      } else {
        // For Android and other platforms
        newToken = await FirebaseMessaging.instance.getToken();
      }
      
      if (newToken?.isNotEmpty ?? false) {
        _currentToken = newToken;
        await _updateTokenInFirestore(newToken!);
        _logger.info('✅ FCM token force refreshed: $newToken');
        FCMErrorMonitor.reportSuccess('force_refresh_token');
      } else {
        _logger.warning('⚠️ Force refresh returned null token');
        FCMErrorMonitor.reportError('force_refresh_token_null', 'Token refresh returned null', null);
      }
    } catch (e) {
      _logger.severe('❌ Failed to force refresh FCM token: $e');
      FCMErrorMonitor.reportError('force_refresh_token', e, null);
    }
  }
  
  /// Clear token from cache and Firestore (useful for logout)
  Future<void> clearToken() async {
    try {
      final uid = Get.find<fiaranow.AuthState>().uid;
      if (uid.isNotEmpty) {
        await FirebaseFirestore.instance.collection('mobile_users').doc(uid).update({
          'fcmToken': FieldValue.delete(),
          'fcmTokenUpdatedAt': FieldValue.serverTimestamp(),
        });
      }
      
      _currentToken = null;
      _retryTimer?.cancel();
      _retryTimer = null;
      
      _logger.info('✅ FCM token cleared');
      FCMErrorMonitor.reportSuccess('clear_token');
    } catch (e) {
      _logger.severe('❌ Failed to clear FCM token: $e');
      FCMErrorMonitor.reportError('clear_token', e, null);
    }
  }
}