/**
 * Calculate Trip Costs Runner for Emulator
 * 
 * This module runs the actual calculateTripCosts function logic
 * against the Firebase emulator for development testing.
 */

const path = require('path');

module.exports = async function calculateTripCosts() {
  // Import the compiled JavaScript functions from the build directory
  // This happens after environment setup in the main runner
  const functionsPath = path.resolve(__dirname, '../../../firebase/functions/lib');
  const { checkDriverRequestTimeouts } = require(path.join(functionsPath, 'driver_request_timeout'));
  const { checkUpcomingReservations, monitorNotificationReliability } = require(path.join(functionsPath, 'reservation_notifications'));
  const { db } = require(path.join(functionsPath, 'config'));
  
  try {
    const startTime = Date.now();

    console.log('Starting trip cost calculation...');

    // Track execution count for periodic checks
    const executionCount = Math.floor(Date.now() / 60000) % 60; // Simple counter based on time
    const shouldCheckReservations = true; // Check every minute
    const shouldMonitorNotifications = executionCount === 0; // Every hour

    // Check for driver request timeouts (actual function call)
    console.log('Checking for driver request timeouts...');
    try {
      await checkDriverRequestTimeouts();
      console.log('Driver request timeout check completed');
    } catch (error) {
      console.error('Error checking driver request timeouts:', error);
    }

    // Check for reservation reminders
    if (shouldCheckReservations) {
      console.log('Checking for upcoming reservation reminders...');
      try {
        await checkUpcomingReservations();
        console.log('Reservation reminder check completed');
      } catch (error) {
        console.error('Error checking reservations:', error);
      }
    }

    // Monitor notification reliability every hour
    if (shouldMonitorNotifications) {
      console.log('Monitoring notification reliability...');
      try {
        await monitorNotificationReliability();
        console.log('Notification reliability monitoring completed');
      } catch (error) {
        console.error('Error monitoring notifications:', error);
      }
    }

    // Use fiaranow as tenant ID for emulator
    const tenantId = 'fiaranow';
    
    // Query active trips
    const activeTripsSnapshot = await db
      .collection(`tenants/${tenantId}/trips`)
      .where('status', '==', 'inProgress')
      .get();

    const totalActiveTripCount = activeTripsSnapshot.size;
    console.log(`Found ${totalActiveTripCount} active trips in tenant ${tenantId}`);

    if (totalActiveTripCount === 0) {
      console.log('No active trips to process');
    } else {
      console.log(`Processing ${totalActiveTripCount} trips...`);
      
      // In emulator mode, just log what would be processed
      activeTripsSnapshot.docs.forEach((doc, index) => {
        const tripData = doc.data();
        console.log(`  Trip ${index + 1}/${totalActiveTripCount}: ${doc.id} (driver: ${tripData.driver?.id || 'unknown'})`);
      });
    }

    const duration = Date.now() - startTime;
    console.log(`Trip cost calculation completed in ${duration}ms`);
    console.log(`Summary: ${totalActiveTripCount} trips processed, ${shouldMonitorNotifications ? 'with' : 'without'} notification monitoring`);

  } catch (error) {
    console.error('Error in calculateTripCosts:', error);
    throw error;
  }
};