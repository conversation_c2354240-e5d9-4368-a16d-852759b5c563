/**
 * Check Driver Request Timeouts Runner for Emulator
 * 
 * This module runs the actual checkDriverRequestTimeouts function
 * against the Firebase emulator for development testing.
 */

const path = require('path');

module.exports = async function runCheckDriverRequestTimeouts() {
  // Import the compiled JavaScript function from the build directory
  // This happens after environment setup in the main runner
  const functionsPath = path.resolve(__dirname, '../../../firebase/functions/lib');
  const { checkDriverRequestTimeouts } = require(path.join(functionsPath, 'driver_request_timeout'));
  try {
    const startTime = Date.now();
    
    console.log('Starting driver request timeout check...');
    console.log(`Timeout threshold: 60 seconds`);
    
    // Call the actual function
    await checkDriverRequestTimeouts();
    
    const duration = Date.now() - startTime;
    console.log(`Driver request timeout check completed in ${duration}ms`);
    
  } catch (error) {
    console.error('Error in checkDriverRequestTimeouts:', error);
    throw error;
  }
};