/**
 * Cleanup Archived Chats Runner for Emulator
 * 
 * This module runs the actual cleanupArchivedChats function logic
 * against the Firebase emulator for development testing.
 */

const path = require('path');

module.exports = async function cleanupArchivedChats() {
  // Import the compiled JavaScript functions from the build directory
  // This happens after environment setup in the main runner
  const functionsPath = path.resolve(__dirname, '../../../firebase/functions/lib');
  const { admin } = require(path.join(functionsPath, 'config'));
  const { getTenantCollection, getDefaultTenantId } = require(path.join(functionsPath, 'tenant_utils'));
  const { Timestamp } = admin.firestore;
  
  try {
    const startTime = Date.now();

    console.log('Starting archived chat cleanup...');

    const thirtyDaysAgo = new Date();
    thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);
    
    const defaultTenantId = getDefaultTenantId();

    // Find archived chat sessions older than 30 days
    const archivedChats = await getTenantCollection(defaultTenantId, "chat_sessions")
      .where("status", "==", "archived")
      .where("lastMessageAt", "<", Timestamp.fromDate(thirtyDaysAgo))
      .get();

    console.log(`Found ${archivedChats.size} archived chat sessions older than 30 days`);

    if (archivedChats.empty) {
      console.log('No old archived chats to cleanup');
      return;
    }

    let deletedSessions = 0;
    let deletedMessages = 0;

    const deletePromises = archivedChats.docs.map(async (doc) => {
      const sessionData = doc.data();
      console.log(`Processing chat session ${doc.id} (last message: ${sessionData.lastMessageAt?.toDate()?.toISOString()})`);

      try {
        // Delete all messages in the session
        const messagesSnapshot = await doc.ref.collection("chat_messages").get();
        console.log(`  Found ${messagesSnapshot.size} messages to delete`);
        
        const messageDeletePromises = messagesSnapshot.docs.map(msgDoc => msgDoc.ref.delete());
        await Promise.all(messageDeletePromises);
        deletedMessages += messagesSnapshot.size;

        // Delete the session
        await doc.ref.delete();
        deletedSessions++;

      } catch (error) {
        console.error(`Error deleting chat session ${doc.id}:`, error);
        throw error;
      }
    });

    await Promise.all(deletePromises);

    console.log(`Cleanup completed:`);
    console.log(`  Deleted sessions: ${deletedSessions}`);
    console.log(`  Deleted messages: ${deletedMessages}`);

    const duration = Date.now() - startTime;
    console.log(`Archived chat cleanup completed in ${duration}ms`);

  } catch (error) {
    console.error('Error in cleanupArchivedChats:', error);
    throw error;
  }
};