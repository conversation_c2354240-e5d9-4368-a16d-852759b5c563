/**
 * Generate Feedback Stats Runner for Emulator
 * 
 * This module runs the actual generateFeedbackStats function logic
 * against the Firebase emulator for development testing.
 */

const path = require('path');

module.exports = async function generateFeedbackStats() {
  // Import the compiled JavaScript functions from the build directory
  // This happens after environment setup in the main runner
  const functionsPath = path.resolve(__dirname, '../../../firebase/functions/lib');
  const { db, admin } = require(path.join(functionsPath, 'config'));
  const { getTenantCollection, getDefaultTenantId } = require(path.join(functionsPath, 'tenant_utils'));
  const { Timestamp } = admin.firestore;
  
  try {
    const startTime = Date.now();

    console.log('Starting feedback statistics generation...');

    const defaultTenantId = getDefaultTenantId();
    const yesterday = new Date();
    yesterday.setDate(yesterday.getDate() - 1);
    yesterday.setHours(0, 0, 0, 0);

    const today = new Date();
    today.setHours(0, 0, 0, 0);

    // Get all feedback from yesterday
    const feedbackSnapshot = await getTenantCollection(defaultTenantId, "feedbacks")
      .where("createdAt", ">=", Timestamp.fromDate(yesterday))
      .where("createdAt", "<", Timestamp.fromDate(today))
      .get();

    console.log(`Found ${feedbackSnapshot.size} feedbacks from yesterday`);

    if (feedbackSnapshot.empty) {
      console.log('No feedback data to process for yesterday');
      return;
    }

    // Calculate statistics
    const stats = {
      date: yesterday,
      totalFeedbacks: feedbackSnapshot.size,
      tripFeedbacks: 0,
      applicationFeedbacks: 0,
      averageRating: 0,
      ratingsCount: 0,
      lowRatings: 0, // 1-2 stars
      mediumRatings: 0, // 3 stars
      highRatings: 0, // 4-5 stars
    };

    let totalRating = 0;

    feedbackSnapshot.forEach((doc) => {
      const feedback = doc.data();

      if (feedback.type === "trip") {
        stats.tripFeedbacks++;
        if (feedback.rating) {
          stats.ratingsCount++;
          totalRating += feedback.rating;

          if (feedback.rating <= 2) stats.lowRatings++;
          else if (feedback.rating === 3) stats.mediumRatings++;
          else stats.highRatings++;
        }
      } else {
        stats.applicationFeedbacks++;
      }
    });

    if (stats.ratingsCount > 0) {
      stats.averageRating = totalRating / stats.ratingsCount;
    }

    console.log('Feedback Statistics Generated:');
    console.log(`  Date: ${yesterday.toISOString().split("T")[0]}`);
    console.log(`  Total feedbacks: ${stats.totalFeedbacks}`);
    console.log(`  Trip feedbacks: ${stats.tripFeedbacks}`);
    console.log(`  Application feedbacks: ${stats.applicationFeedbacks}`);
    console.log(`  Average rating: ${stats.averageRating.toFixed(2)}`);
    console.log(`  Rating counts: Low(1-2): ${stats.lowRatings}, Medium(3): ${stats.mediumRatings}, High(4-5): ${stats.highRatings}`);

    // Save statistics
    await getTenantCollection(defaultTenantId, "feedback_statistics").doc(yesterday.toISOString().split("T")[0]).set(stats);
    console.log(`Saved feedback statistics for ${yesterday.toISOString().split("T")[0]}`);

    console.log(`Generated feedback statistics for ${yesterday.toISOString().split("T")[0]}:`, stats);

    const duration = Date.now() - startTime;
    console.log(`Feedback statistics generation completed in ${duration}ms`);

  } catch (error) {
    console.error('Error in generateFeedbackStats:', error);
    throw error;
  }
};