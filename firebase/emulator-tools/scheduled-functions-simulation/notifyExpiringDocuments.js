/**
 * Notify Expiring Documents Runner for Emulator
 * 
 * This module runs the actual notifyExpiringDocuments function logic
 * against the Firebase emulator for development testing.
 */

const path = require('path');

module.exports = async function notifyExpiringDocuments() {
  // Import the compiled JavaScript function from the build directory
  // This happens after environment setup in the main runner
  const functionsPath = path.resolve(__dirname, '../../../firebase/functions/lib');
  
  // The actual function is exported from document_operations.ts
  // We need to extract the core logic since we can't call the onSchedule wrapper directly
  const { db } = require(path.join(functionsPath, 'config'));
  const { sendNotificationToUser } = require(path.join(functionsPath, 'notification_operations'));
  
  try {
    const startTime = Date.now();

    console.log('Starting document expiry notification check...');

    const now = new Date();
    const thirtyDaysFromNow = new Date(now.getTime() + (30 * 24 * 60 * 60 * 1000));
    const sevenDaysFromNow = new Date(now.getTime() + (7 * 24 * 60 * 60 * 1000));
    
    // Query all driver documents
    const documentsSnapshot = await db
      .collectionGroup("driver_documents")
      .where("status", "==", "approved")
      .where("expiryDate", "<=", thirtyDaysFromNow)
      .get();

    console.log(`Found ${documentsSnapshot.size} documents expiring within 30 days`);

    if (documentsSnapshot.empty) {
      console.log('No expiring documents found');
      return;
    }

    const notifications = [];
    const adminNotifications = new Map();

    for (const doc of documentsSnapshot.docs) {
      const docData = doc.data();
      const expiryDate = docData.expiryDate.toDate();
      const daysUntilExpiry = Math.floor((expiryDate.getTime() - now.getTime()) / (1000 * 60 * 60 * 24));
      
      // Check if we should send notification
      let shouldNotify = false;
      let urgency = "low";
      
      if (daysUntilExpiry <= 1) {
        shouldNotify = true;
        urgency = "high";
      } else if (daysUntilExpiry <= 7 && expiryDate <= sevenDaysFromNow) {
        shouldNotify = true;
        urgency = "medium";
      } else if (daysUntilExpiry <= 30 && expiryDate <= thirtyDaysFromNow) {
        shouldNotify = true;
        urgency = "low";
      }
      
      if (shouldNotify) {
        // Extract driver UID from document path
        const pathParts = doc.ref.path.split("/");
        const driverUID = pathParts[1]; // mobile_users/{uid}/driver_documents/{docId}
        
        // Notification to driver
        const driverMessage = daysUntilExpiry <= 0
          ? `Your ${docData.documentName} has expired`
          : `Your ${docData.documentName} expires in ${daysUntilExpiry} day${daysUntilExpiry !== 1 ? "s" : ""}`;
        
        const notificationData = {
          type: "document_expiry",
          documentId: doc.id,
          documentName: docData.documentName,
          documentType: docData.documentType,
          expiryDate: expiryDate.toISOString(),
          daysUntilExpiry,
          urgency,
        };
        
        console.log(`Sending notification to driver ${driverUID}: ${driverMessage}`);
        
        notifications.push(
          sendNotificationToUser(
            driverUID,
            driverMessage,
            "Document Expiry Notice",
            notificationData
          ).catch(err => console.error(`Failed to notify driver ${driverUID}:`, err))
        );
        
        // Collect for admin notifications per tenant
        const tenantIds = docData.tenantIDs || [];
        for (const tid of tenantIds) {
          if (!adminNotifications.has(tid)) {
            adminNotifications.set(tid, []);
          }
          adminNotifications.get(tid).push({
            driverUID,
            documentName: docData.documentName,
            daysUntilExpiry,
            urgency,
          });
        }
      }
    }
    
    // Send admin notifications per tenant
    for (const [tid, docs] of adminNotifications) {
      // Get tenant admins
      const adminsSnapshot = await db
        .collectionGroup("tenants")
        .where("tenantId", "==", tid)
        .where("isActive", "==", true)
        .where("role", ">=", 1) // ADMIN or higher
        .get();
      
      const highUrgencyCount = docs.filter(d => d.urgency === "high").length;
      const mediumUrgencyCount = docs.filter(d => d.urgency === "medium").length;
      const lowUrgencyCount = docs.filter(d => d.urgency === "low").length;
      
      let adminMessage = `${docs.length} driver document${docs.length !== 1 ? "s" : ""} expiring soon`;
      if (highUrgencyCount > 0) {
        adminMessage = `URGENT: ${highUrgencyCount} document${highUrgencyCount !== 1 ? "s" : ""} expiring within 24 hours`;
      }
      
      console.log(`Sending admin notification for tenant ${tid}: ${adminMessage}`);
      
      // Send notification to each admin
      for (const adminDoc of adminsSnapshot.docs) {
        const adminData = adminDoc.data();
        notifications.push(
          sendNotificationToUser(
            adminData.uid,
            adminMessage,
            "Document Expiry Alert",
            {
              type: "admin_document_expiry_summary",
              tenantId: tid,
              expiringDocuments: docs,
              highUrgencyCount,
              mediumUrgencyCount,
              lowUrgencyCount,
            }
          ).catch(err => console.error(`Failed to notify admin ${adminData.uid}:`, err))
        );
      }
    }
    
    // Wait for all notifications to be sent
    await Promise.all(notifications);
    
    const duration = Date.now() - startTime;
    console.log(`Document expiry notification check completed in ${duration}ms`);
    console.log(`Total notifications sent: ${notifications.length}`);

  } catch (error) {
    console.error('Error in notifyExpiringDocuments:', error);
    throw error;
  }
};