import * as admin from "firebase-admin";
import * as path from "path";
import * as fs from "fs";

// Check if we're running in the emulator
const isEmulator = process.env.FUNCTIONS_EMULATOR === "true";

if (isEmulator) {
  // In emulator, use service account credentials for FCM
  // Try multiple possible paths for the service account file
  const possiblePaths = [
    // Docker container path
    '/workspace/firebase/fiaranow-firebase-adminsdk-sx0g7-36d6d8a0de.json',
    // Local development paths
    path.join(__dirname, '../../fiaranow-firebase-adminsdk-sx0g7-36d6d8a0de.json'),
    path.join(__dirname, '../fiaranow-firebase-adminsdk-sx0g7-36d6d8a0de.json'),
    // Relative to functions directory
    path.join(process.cwd(), '../fiaranow-firebase-adminsdk-sx0g7-36d6d8a0de.json'),
    path.join(process.cwd(), 'fiaranow-firebase-adminsdk-sx0g7-36d6d8a0de.json'),
  ];
  
  let serviceAccountPath: string | undefined;
  for (const possiblePath of possiblePaths) {
    if (fs.existsSync(possiblePath)) {
      serviceAccountPath = possiblePath;
      break;
    }
  }
  
  if (serviceAccountPath) {
    const serviceAccount = require(serviceAccountPath);
    admin.initializeApp({
      credential: admin.credential.cert(serviceAccount),
      projectId: serviceAccount.project_id
    });
    console.log(`Firebase Admin initialized with service account credentials from: ${serviceAccountPath}`);
  } else {
    // Fallback to default initialization if service account not found
    console.warn('Service account file not found in any of the expected locations, FCM notifications may fail');
    console.warn('Searched paths:', possiblePaths);
    admin.initializeApp();
  }
} else {
  // Production uses application default credentials
  admin.initializeApp();
}

export const db = admin.firestore();
export { admin };
