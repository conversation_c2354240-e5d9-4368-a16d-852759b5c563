import { firestore } from "firebase-admin";
import axios from "axios";
import { onCall, HttpsError } from "firebase-functions/v2/https";
import { logger } from "firebase-functions/v2";

export interface LatLng {
  lat: number;
  lon: number;
}

export interface RouteData {
  distanceMeters: number;
  durationSeconds: number;
  polyline: string;
  bounds: {
    northeast: {
      lat: number;
      lng: number;
    };
    southwest: {
      lat: number;
      lng: number;
    };
  };
}

export interface Trip {
  startLocation: LatLng;
  arrivalLocation: LatLng;
  driverLocation?: LatLng;
  distance?: number;
  status:
    | "preparing"
    | "requestingDriver"
    | "driverApproaching"
    | "driverAwaiting"
    | "inProgress"
    | "completed"
    | "cancelled"
    | "paid"
    | "reserved";
  driverAwaitingTime?: firestore.Timestamp;
  driverStartTime?: firestore.Timestamp;
  passengerStartTime?: firestore.Timestamp;
  pickupTime?: firestore.Timestamp;
  paymentMethod?: string;
  routeData?: RouteData;
  driverRouteData?: RouteData;
  uidChosenDriver?: string;
  uidPassenger: string;
  driverNotificationSent?: boolean;
  driverDismissed: boolean;
  routeDataIds?: {
    main?: string;
    driver?: string;
    final?: string;
    overviews?: string[];
    selectedOverviewId?: string;
  };
  passengerCount?: number;
  startLocationName?: string;
  arrivalLocationName?: string;
}

const apiKey = "AIzaSyC5gn-4e1bZvCv4MVkopVvZAS642Up2EK8";

export async function createRouteData(
  startLocation: LatLng,
  arrivalLocation: LatLng,
  polylineQuality: "HIGH_QUALITY" | "OVERVIEW" = "HIGH_QUALITY"
): Promise<RouteData> {
  logger.info("🚀 createRouteData called", {
    startLocation,
    arrivalLocation,
    polylineQuality,
  });
  
  try {
    const requestPayload = {
      origin: {
        location: {
          latLng: {
            latitude: startLocation.lat,
            longitude: startLocation.lon,
          },
        },
      },
      destination: {
        location: {
          latLng: {
            latitude: arrivalLocation.lat,
            longitude: arrivalLocation.lon,
          },
        },
      },
      travelMode: "DRIVE",
      routingPreference: "TRAFFIC_AWARE_OPTIMAL",
      trafficModel: "PESSIMISTIC",
      polylineQuality: polylineQuality,
      computeAlternativeRoutes: false,
      routeModifiers: {
        avoidTolls: false,
        avoidHighways: false,
        avoidFerries: false,
      },
      languageCode: "fr-FR",
      units: "METRIC",
    };
    
    logger.info("📤 Google Routes API request payload", {
      requestPayload: JSON.stringify(requestPayload),
    });
    
    const response = await axios.post(
      `https://routes.googleapis.com/directions/v2:computeRoutes`,
      requestPayload,
      {
        headers: {
          "Content-Type": "application/json",
          "X-Goog-Api-Key": apiKey,
          "X-Goog-FieldMask": "routes.duration,routes.distanceMeters,routes.legs,routes.legs.distanceMeters,routes.legs.duration,routes.legs.polyline.encodedPolyline,routes.legs.localizedValues,routes.viewport",
        },
      }
    );

    logger.info("📥 Google Routes API raw response", {
      status: response.status,
      statusText: response.statusText,
      hasData: !!response.data,
      hasRoutes: !!(response.data?.routes),
      routesLength: response.data?.routes?.length || 0,
      fullResponse: JSON.stringify(response.data),
    });

    // Validate response structure
    if (!response.data || !response.data.routes || response.data.routes.length === 0) {
      throw new Error("No routes found in Google Routes API response");
    }

    const route = response.data.routes[0];
    logger.info("🛣️ Route data", {
      hasLegs: !!route.legs,
      legsLength: route.legs?.length || 0,
      routeKeys: Object.keys(route),
    });
    
    if (!route.legs || route.legs.length === 0) {
      throw new Error("No legs found in route");
    }

    const leg = route.legs[0];
    
    // Log the entire leg object to debug the structure
    logger.info("🦵 Full leg object for debugging", {
      fullLeg: JSON.stringify(leg),
    });
    
    logger.info("🦵 Leg data", {
      legKeys: Object.keys(leg),
      distanceMeters: leg.distanceMeters,
      distanceMetersType: typeof leg.distanceMeters,
      hasPolyline: !!leg.polyline,
      hasEncodedPolyline: !!(leg.polyline?.encodedPolyline),
    });

    // Check for alternative field names
    logger.info("🔍 Checking for distance in various fields", {
      distanceMeters: leg.distanceMeters,
      distance: (leg as any).distance,
      distanceText: (leg as any).distanceText,
      meters: (leg as any).meters,
      localizedValues: (leg as any).localizedValues,
      availableFields: Object.keys(leg).filter(key => key.toLowerCase().includes('dist') || key.toLowerCase().includes('meter')),
    });

    // Validate required fields
    logger.info("🔍 Validating distanceMeters", {
      value: leg.distanceMeters,
      type: typeof leg.distanceMeters,
      isNumber: typeof leg.distanceMeters === 'number',
      isUndefined: leg.distanceMeters === undefined,
      isNull: leg.distanceMeters === null,
    });
    
    // Handle missing or invalid distanceMeters
    let distanceMeters = leg.distanceMeters;
    
    // Fallback 1: Check if route has distanceMeters
    if (typeof distanceMeters !== 'number' && route.distanceMeters) {
      logger.warn("⚠️ Using route.distanceMeters as fallback", {
        routeDistanceMeters: route.distanceMeters,
        legDistanceMeters: leg.distanceMeters,
      });
      distanceMeters = route.distanceMeters;
    }
    
    // Fallback 2: Check alternative field names in leg
    if (typeof distanceMeters !== 'number') {
      const alternativeFields = ['distance', 'meters', 'distanceText'];
      for (const field of alternativeFields) {
        if ((leg as any)[field] && typeof (leg as any)[field] === 'number') {
          logger.warn(`⚠️ Using leg.${field} as fallback`, {
            fieldName: field,
            fieldValue: (leg as any)[field],
          });
          distanceMeters = (leg as any)[field];
          break;
        }
      }
    }
    
    // Fallback 3: Parse from localizedValues
    if (typeof distanceMeters !== 'number' && leg.localizedValues?.distance?.text) {
      const distanceText = leg.localizedValues.distance.text;
      logger.info("🔍 Attempting to parse distance from localizedValues", {
        distanceText,
      });
      
      // Parse distance from text like "1 m", "1.5 km", etc.
      const match = distanceText.match(/^([\d,\.]+)\s*(m|km|mi)?$/);
      if (match) {
        const value = parseFloat(match[1].replace(',', '.'));
        const unit = match[2] || 'm';
        
        let meters = value;
        if (unit === 'km') {
          meters = value * 1000;
        } else if (unit === 'mi') {
          meters = value * 1609.34;
        }
        
        logger.warn("⚠️ Parsed distance from localizedValues", {
          originalText: distanceText,
          parsedValue: value,
          unit,
          distanceMeters: meters,
        });
        
        distanceMeters = meters;
      }
    }
    
    // Final validation
    if (typeof distanceMeters !== 'number') {
      logger.error("❌ Invalid distanceMeters after all fallbacks", {
        legData: JSON.stringify(leg),
        distanceMeters: leg.distanceMeters,
        distanceMetersType: typeof leg.distanceMeters,
        routeKeys: Object.keys(route),
        fullRoute: JSON.stringify(route),
        routeDistanceMeters: route.distanceMeters,
      });
      throw new Error(`Invalid distanceMeters: ${leg.distanceMeters} (route.distanceMeters: ${route.distanceMeters})`);
    }
    
    // Ensure minimum distance of 1 meter for very close locations
    if (distanceMeters < 1) {
      logger.warn("⚠️ Distance is less than 1 meter, setting to minimum", {
        originalDistance: distanceMeters,
        minimumDistance: 1,
      });
      distanceMeters = 1;
    }

    logger.info("🔍 Validating duration", {
      value: route.duration,
      type: typeof route.duration,
      isString: typeof route.duration === 'string',
    });
    
    if (!route.duration || typeof route.duration !== 'string') {
      logger.error("❌ Invalid duration", {
        routeData: JSON.stringify(route),
        duration: route.duration,
        durationType: typeof route.duration,
      });
      throw new Error(`Invalid duration: ${route.duration}`);
    }

    if (!leg.polyline || !leg.polyline.encodedPolyline) {
      throw new Error("Missing polyline data");
    }

    if (!route.viewport || !route.viewport.high || !route.viewport.low) {
      throw new Error("Missing viewport data");
    }

    const distanceInMeters = distanceMeters;
    const durationInSeconds = parseInt(route.duration.slice(0, -1), 10);

    if (isNaN(durationInSeconds)) {
      throw new Error(`Invalid duration format: ${route.duration}`);
    }

    const result = {
      distanceMeters: distanceInMeters,
      durationSeconds: durationInSeconds,
      polyline: leg.polyline.encodedPolyline,
      bounds: {
        northeast: {
          lat: route.viewport.high.latitude,
          lng: route.viewport.high.longitude,
        },
        southwest: {
          lat: route.viewport.low.latitude,
          lng: route.viewport.low.longitude,
        },
      },
    };
    
    logger.info("✅ createRouteData successful", {
      distanceMeters: result.distanceMeters,
      durationSeconds: result.durationSeconds,
      hasPolyline: !!result.polyline,
      polylineLength: result.polyline?.length || 0,
    });
    
    return result;
  } catch (error) {
    logger.error("Error creating route data", {
      error: error instanceof Error ? error.message : String(error),
      startLocation,
      arrivalLocation,
      polylineQuality
    });
    throw error;
  }
}

export async function createMultipleRoutesData(
  start: LatLng,
  end: LatLng,
  polylineQuality: "HIGH_QUALITY" | "OVERVIEW" = "HIGH_QUALITY"
): Promise<RouteData[]> {
  const response = await axios.post(
    "https://routes.googleapis.com/directions/v2:computeRoutes",
    {
      origin: { location: { latLng: { latitude: start.lat, longitude: start.lon } } },
      destination: { location: { latLng: { latitude: end.lat, longitude: end.lon } } },
      travelMode: "DRIVE",
      routingPreference: "TRAFFIC_AWARE_OPTIMAL",
      trafficModel: "PESSIMISTIC",
      polylineQuality,
      computeAlternativeRoutes: true,
      routeModifiers: { avoidTolls: false, avoidHighways: false, avoidFerries: false },
      languageCode: "fr-FR",
      units: "METRIC",
    },
    {
      headers: {
        "Content-Type": "application/json",
        "X-Goog-Api-Key": apiKey,
        "X-Goog-FieldMask": "routes.duration,routes.distanceMeters,routes.legs,routes.legs.distanceMeters,routes.legs.duration,routes.legs.polyline.encodedPolyline,routes.legs.localizedValues,routes.viewport",
      },
    }
  );

  return response.data.routes.map((r: any) => {
    const leg = r.legs[0];
    const durationInSeconds = parseInt(r.duration.slice(0, -1), 10);
    return {
      distanceMeters: leg.distanceMeters,
      durationSeconds: durationInSeconds,
      polyline: leg.polyline.encodedPolyline,
      bounds: {
        northeast: { lat: r.viewport.high.latitude, lng: r.viewport.high.longitude },
        southwest: { lat: r.viewport.low.latitude, lng: r.viewport.low.longitude },
      },
    };
  });
}

// Firebase Functions for route calculation
export const getRouteData = onCall(
  {
    region: "europe-west3",
    timeoutSeconds: 5,
    memory: "512MiB",
    concurrency: 1000,
    maxInstances: 1,
  },
  async (request) => {
    if (!request.auth) {
      throw new HttpsError("unauthenticated", "The function must be called while authenticated.");
    }

    const { startLocation, arrivalLocation, highQuality } = request.data as {
      startLocation: LatLng;
      arrivalLocation: LatLng;
      highQuality?: boolean;
    };
    if (!startLocation || !arrivalLocation) {
      throw new HttpsError("invalid-argument", "Start and arrival locations are required.");
    }

    try {
      const routeData = await createRouteData(startLocation, arrivalLocation, highQuality ? "HIGH_QUALITY" : "OVERVIEW");
      logger.info("Route data calculated", {
        distanceMeters: routeData.distanceMeters,
        durationSeconds: routeData.durationSeconds,
      });

      return {
        polyline: routeData.polyline,
        bounds: routeData.bounds,
        durationSeconds: routeData.durationSeconds,
        distanceMeters: routeData.distanceMeters,
      };
    } catch (error) {
      logger.error("Error calculating route data", { error, startLocation, arrivalLocation });
      throw error;
    }
  }
);

export const getMultipleRoutesData = onCall(
  {
    region: "europe-west3",
    timeoutSeconds: 5,
    memory: "512MiB",
    concurrency: 1000,
    maxInstances: 1,
  },
  async (request) => {
    if (!request.auth) {
      throw new HttpsError("unauthenticated", "Authentication is required.");
    }

    const { startLocation, arrivalLocation, highQuality } = request.data;
    if (!startLocation || !arrivalLocation) {
      throw new HttpsError("invalid-argument", "Start and arrival locations are required.");
    }

    try {
      const data = await createMultipleRoutesData(startLocation, arrivalLocation, highQuality ? "HIGH_QUALITY" : "OVERVIEW");

      logger.info("Multiple routes data calculated", {
        routeCount: data.length,
        startLocation,
        arrivalLocation,
      });

      return data;
    } catch (error) {
      logger.error("Error calculating multiple routes data", {
        error: error instanceof Error ? error.message : String(error),
        startLocation,
        arrivalLocation,
      });

      throw error;
    }
  }
);
