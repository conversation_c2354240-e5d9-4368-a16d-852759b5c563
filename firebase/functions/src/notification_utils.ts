import { admin } from "./config";
import { logger } from "firebase-functions/v2";
import { isProduction } from "./environment";

/**
 * Allowed user IDs for push notifications in development mode
 * Push notifications will only be sent to these users when running in dev mode
 */
const ALLOWED_DEV_USER_IDS = [
  "c5YHX79mg8OgHoyF12JBEffYwnE3",
  "oEay2mlxWgXAYRyKxvuIjL54pII3", 
  "zH5hYbVBkwPUc99Q2asR5TIXnks1"
];

/**
 * Result of sending a push notification
 */
export interface PushNotificationResult {
  success: boolean;
  messageId?: string;
  error?: {
    code: string;
    message: string;
    isTokenError: boolean;
  };
}

/**
 * Send push notification with development mode guard
 * In production: sends all notifications
 * In development: only sends notifications to allowed user IDs
 * @returns PushNotificationResult indicating success/failure
 */
export async function sendPushNotification(message: admin.messaging.Message, context: string, userId: string): Promise<PushNotificationResult> {
  const isProd = isProduction();
  
  // In production, always send notifications
  // In development, only send if userId is in the allowed list
  const shouldSend = isProd || (userId && ALLOWED_DEV_USER_IDS.includes(userId));

  if (shouldSend) {
    try {
      // Send the actual notification
      const messageId = await admin.messaging().send(message);

      const environmentEmoji = isProd ? "📱" : "🔔";
      const environmentLabel = isProd ? "PRODUCTION" : "DEV MODE";

      logger.info(`${environmentEmoji} [${environmentLabel}] Push notification sent successfully`, {
        context,
        environment: isProd ? "production" : "development",
        to: "token" in message ? "single_device" : "topic",
        userId: userId,
        title: message.notification?.title,
        body: message.notification?.body,
        messageId,
      });
      
      return { success: true, messageId };
    } catch (error: any) {
      // Handle FCM errors gracefully
      const isTokenError = error.code === 'messaging/registration-token-not-registered' || 
          error.code === 'messaging/invalid-registration-token' ||
          (error.message && error.message.includes('Requested entity was not found'));
      
      if (isTokenError) {
        logger.warn(`FCM token invalid or expired for user ${userId}`, {
          context,
          userId,
          errorCode: error.code,
          errorMessage: error.message,
          title: message.notification?.title,
          body: message.notification?.body,
        });
        
        // Return failure result for token errors
        return {
          success: false,
          error: {
            code: error.code || 'token-error',
            message: error.message,
            isTokenError: true,
          }
        };
      }
      
      // Log unexpected errors and return failure
      logger.error(`Failed to send push notification`, {
        context,
        userId,
        error: error.message,
        errorCode: error.code,
        title: message.notification?.title,
        body: message.notification?.body,
      });
      
      return {
        success: false,
        error: {
          code: error.code || 'unknown',
          message: error.message,
          isTokenError: false,
        }
      };
    }
  } else {
    // Log the notification details in dev mode when disabled
    const token = "token" in message ? message.token : null;
    const blockedReason = !isProd && !ALLOWED_DEV_USER_IDS.includes(userId) 
      ? `User ${userId} not in allowed dev list` 
      : "Notifications disabled";
      
    logger.info(`🔔 [DEV MODE - BLOCKED] Push notification not sent:`, {
      context,
      environment: "development",
      reason: blockedReason,
      userId: userId,
      title: message.notification?.title,
      body: message.notification?.body,
      data: message.data,
      token: token ? `${token.substring(0, 20)}...` : "N/A",
      android: message.android ? "configured" : "not_configured",
      apns: message.apns ? "configured" : "not_configured",
      webpush: message.webpush ? "configured" : "not_configured",
    });
    
    return {
      success: false,
      error: {
        code: 'dev-mode-blocked',
        message: blockedReason,
        isTokenError: false,
      }
    };
  }
}
