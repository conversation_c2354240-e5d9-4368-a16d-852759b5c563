import { CollectionReference, DocumentReference } from "firebase-admin/firestore";
import { logger } from "firebase-functions/v2";
import { RouteData } from "./navigation";
import { RouteDataDocument } from "./models/RouteDataDocument";
import { db } from "./config";
const DEFAULT_TENANT_ID = "fiaranow";

/**
 * Get the tenant-specific path for a collection
 */
export function getTenantPath(tenantId: string, collection: string): string {
  return `tenants/${tenantId}/${collection}`;
}

/**
 * Validate if an admin has access to a specific tenant
 */
export async function validateTenantAccess(adminUid: string, tenantId: string): Promise<boolean> {
  try {
    const tenantAccessDoc = await db
      .doc(`admin_users/${adminUid}/tenants/${tenantId}`)
      .get();

    if (!tenantAccessDoc.exists) {
      return false;
    }

    const tenantAccess = tenantAccessDoc.data();
    return tenantAccess?.isActive === true;
  } catch (error) {
    console.error("Error validating tenant access:", error);
    return false;
  }
}

/**
 * Get admin access details for a specific tenant
 */
export async function getAdminAccess(adminUid: string, tenantId: string): Promise<{ role: number, isActive: boolean } | null> {
  try {
    const tenantAccessDoc = await db
      .doc(`admin_users/${adminUid}/tenants/${tenantId}`)
      .get();

    if (!tenantAccessDoc.exists) {
      return null;
    }

    const tenantAccess = tenantAccessDoc.data();
    if (!tenantAccess || !tenantAccess.isActive) {
      return null;
    }

    return {
      role: tenantAccess.role || 0,
      isActive: tenantAccess.isActive
    };
  } catch (error) {
    console.error("Error getting admin access:", error);
    return null;
  }
}

/**
 * Get a tenant-specific collection reference
 */
export function getTenantCollection(tenantId: string, collection: string): CollectionReference {
  return db.collection(getTenantPath(tenantId, collection));
}

/**
 * Get the admin's role for a specific tenant
 */
export async function getAdminTenantRole(adminUid: string, tenantId: string): Promise<number | null> {
  try {
    const tenantAccessDoc = await db
      .doc(`admin_users/${adminUid}/tenants/${tenantId}`)
      .get();

    if (!tenantAccessDoc.exists) {
      return null;
    }

    const tenantAccess = tenantAccessDoc.data();
    if (tenantAccess?.isActive !== true) {
      return null;
    }

    return tenantAccess.role ?? null;
  } catch (error) {
    console.error("Error getting admin tenant role:", error);
    return null;
  }
}

/**
 * Check if admin has minimum role for a tenant
 * Roles: 0 = MANAGER, 1 = ADMIN, 2 = SUPER_ADMIN
 */
export async function hasMinimumRole(adminUid: string, tenantId: string, minRole: number): Promise<boolean> {
  const role = await getAdminTenantRole(adminUid, tenantId);
  if (role === null) return false;
  return role >= minRole;
}

/**
 * Get a tenant document reference
 */
export function getTenantDoc(tenantId: string): DocumentReference {
  return db.doc(`tenants/${tenantId}`);
}

/**
 * Get default tenant ID - used for backward compatibility
 */
export function getDefaultTenantId(): string {
  return DEFAULT_TENANT_ID;
}

/**
 * Ensure tenantId is provided or use default
 */
export function ensureTenantId(tenantId?: string): string {
  return tenantId || getDefaultTenantId();
}

/**
 * Get all active tenants from the tenants collection
 */
export async function getAllActiveTenants(): Promise<string[]> {
  try {
    const tenantsSnapshot = await db.collection('tenants')
      .where('isActive', '==', true)
      .get();

    return tenantsSnapshot.docs.map(doc => doc.id);
  } catch (error) {
    logger.error('Error fetching active tenants:', error);
    // Return default tenant if there's an error
    return [getDefaultTenantId()];
  }
}

/**
 * Get route data for a trip.
 * It first checks for embedded routeData, then falls back to fetching from route_data collection.
 */
export async function getRouteDataForTrip(tripId: string, tenantId: string): Promise<RouteData | null> {
  const tripRef = getTenantCollection(tenantId, 'trips').doc(tripId);
  const tripDoc = await tripRef.get();

  if (!tripDoc.exists) {
    logger.warn(`Trip ${tripId} not found in tenant ${tenantId}`);
    return null;
  }

  const tripData = tripDoc.data()!;

  // 1. Check for embedded route data first
  if (tripData.routeData) {
    logger.info(`Using embedded route data for trip ${tripId}`);
    return tripData.routeData as RouteData;
  }

  // 2. Fallback to routeDataIds
  if (tripData.routeDataIds && typeof tripData.routeDataIds === 'object') {
    // Check for main route ID first, then fallback to any available route
    const routeId = tripData.routeDataIds.main || 
                    tripData.routeDataIds.driver || 
                    tripData.routeDataIds.final ||
                    (tripData.routeDataIds.overviews && tripData.routeDataIds.overviews[0]);
    
    if (routeId) {
      logger.info(`Fetching route data with ID ${routeId} for trip ${tripId}`);
      const routeDoc = await getTenantCollection(tenantId, 'route_data').doc(routeId).get();

      if (routeDoc.exists) {
        const routeDataDoc = routeDoc.data() as RouteDataDocument;
        return routeDataDoc.routeData;
      } else {
        logger.warn(`Route data document ${routeId} not found for trip ${tripId}`);
      }
    }
  }

  logger.warn(`No route data could be found for trip ${tripId}`);
  return null;
}

/**
 * Validate tenant role assignment
 * SUPER_ADMIN (role: 2) only allowed in 'fiaranow' tenant
 */
export function validateTenantRole(tenantId: string, role: number): boolean {
  // SUPER_ADMIN (role: 2) only allowed in 'fiaranow' tenant
  if (role === 2 && tenantId !== 'fiaranow') {
    return false;
  }
  // Valid roles: 0=MANAGER, 1=ADMIN, 2=SUPER_ADMIN (fiaranow only)
  return role >= 0 && role <= 2;
}