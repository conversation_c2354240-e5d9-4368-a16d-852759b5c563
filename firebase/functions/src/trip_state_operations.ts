import { on<PERSON><PERSON>, HttpsError } from "firebase-functions/v2/https";
import { FieldValue, DocumentReference } from "firebase-admin/firestore";
import * as logger from "firebase-functions/logger";
import { createActor } from "xstate";
import tripStateMachine, { TripEvent, TripStatus, canTransition } from "./trip_state_machine";
import { logStateTransition } from "./trip_logging";
import { sendAdminNotification } from "./chat_notifications";
import { calculateFinalTripCost, calculateTripEstimate } from "./trip_cost_calculator";
import { getTenantCollection, ensureTenantId } from "./tenant_utils";
import { sendPassengerNotification } from "./notifications";
import { db } from "./config";

interface TransitionResult {
  success: boolean;
  newState: TripStatus;
  error?: string;
}

/**
 * Send notification to admins when a trip is cancelled
 */
async function sendCancellationNotification(
  tripId: string,
  tripData: any,
  cancelledByUid: string,
  cancellerRole: string,
  tenantId: string
): Promise<void> {
  try {
    // Get passenger information
    // Note: mobile_users is a root collection, not tenant-specific
    const passengerDoc = await db.collection("mobile_users").doc(tripData.uidPassenger).get();
    const passengerName = passengerDoc.exists
      ? passengerDoc.data()?.displayName || passengerDoc.data()?.fullName || "Passenger"
      : "Passenger";

    // Get canceller information
    let cancellerName = cancellerRole;
    if (cancellerRole !== "admin") {
      const cancellerDoc = await db.collection("mobile_users").doc(cancelledByUid).get();
      cancellerName = cancellerDoc.exists
        ? cancellerDoc.data()?.displayName || cancellerDoc.data()?.fullName || cancellerRole
        : cancellerRole;
    }

    // Prepare notification details
    const pickupAddress = tripData.startAddress || "Pickup location";
    const destinationAddress = tripData.arrivalAddress || "Destination";

    const title = "Trip Cancelled";
    const body = `Trip from ${pickupAddress} to ${destinationAddress} was cancelled by ${cancellerName}. Passenger: ${passengerName}`;

    await sendAdminNotification(
      "trip_cancelled",
      title,
      body,
      {
        type: "trip_cancelled",
        tripId: tripId,
      },
      tenantId
    );

    logger.info("Trip cancellation notification sent to admins", {
      tripId,
      cancelledBy: cancelledByUid,
      cancellerRole,
    });
  } catch (error) {
    logger.error("Error sending cancellation notification", { tripId, error });
    throw error;
  }
}

async function executeStateTransition(
  tripId: string,
  event: TripEvent,
  userId: string,
  userRole: "passenger" | "driver" | "admin",
  tenantId: string
): Promise<TransitionResult> {
  const tripRef = getTenantCollection(tenantId, "trips").doc(tripId);

  try {
    return await db.runTransaction(async (transaction) => {
      const tripDoc = await transaction.get(tripRef);

      if (!tripDoc.exists) {
        throw new HttpsError("not-found", "Trip not found");
      }

      const tripData = tripDoc.data()!;
      const currentStatus = tripData.status as TripStatus;

      // Check if transition is valid
      if (!canTransition(currentStatus, event.type)) {
        throw new HttpsError("failed-precondition", `Cannot transition from ${currentStatus} with event ${event.type}`);
      }

      // Special handling for DISMISS on final states (cancelled, paid)
      if ((currentStatus === "cancelled" || currentStatus === "paid") && event.type === "DISMISS") {
        // For final states, directly update the dismiss fields without state machine
        const dismissUpdates: any = {
          lastModified: FieldValue.serverTimestamp(),
        };

        if (event.userType === "driver") {
          dismissUpdates.driverDismissed = true;
        } else {
          dismissUpdates.passengerDismissed = true;
        }

        // Update the trip document
        await transaction.update(tripRef, dismissUpdates);

        // Log the state transition
        await logStateTransition(
          tripId,
          currentStatus,
          currentStatus, // State remains the same
          event,
          userId,
          userRole,
          tenantId,
          transaction
        );

        return {
          success: true,
          newState: currentStatus,
          message: "Trip dismissed successfully",
        };
      }

      // For non-final states, use the state machine with proper state restoration
      let newState: TripStatus = currentStatus;
      let updates: any = {};

      // Create a persisted snapshot to restore the state machine to the current state
      const persistedSnapshot = {
        value: currentStatus,
        context: {
          tripId,
          uidPassenger: tripData.uidPassenger,
          uidChosenDriver: tripData.uidChosenDriver,
          status: currentStatus,
          driverLocation: tripData.driverLocation,
          driverDismissed: tripData.driverDismissed,
          passengerDismissed: tripData.passengerDismissed,
          cancelledBy: tripData.cancelledBy || tripData.uidCancelledBy,
          cancelReason: tripData.cancelReason,
          timestamp: FieldValue.serverTimestamp(),
        },
        children: {},
        status: "active" as const,
        output: undefined,
        error: undefined,
        historyValue: {},
      };

      // Create actor with the restored snapshot
      const actor = createActor(tripStateMachine, {
        snapshot: persistedSnapshot,
        input: {
          tripId,
          uidPassenger: tripData.uidPassenger,
          uidChosenDriver: tripData.uidChosenDriver,
          status: currentStatus,
          driverLocation: tripData.driverLocation,
          driverDismissed: tripData.driverDismissed,
          passengerDismissed: tripData.passengerDismissed,
          cancelledBy: tripData.cancelledBy || tripData.uidCancelledBy,
          cancelReason: tripData.cancelReason,
        },
      });

      // Start the actor - it will start in the restored state
      actor.start();

      logger.info("State machine restored to current state", {
        tripId,
        restoredState: currentStatus,
        event: event.type,
      });

      // Send the event and get the new state
      actor.send(event);
      const snapshot = actor.getSnapshot();
      newState = snapshot.value as TripStatus;

      // Build updates based on new state
      updates = {
        status: newState,
        lastModified: FieldValue.serverTimestamp(),
      };

      // Add event-specific updates
      switch (event.type) {
        case "REQUEST_DRIVER":
          updates.uidChosenDriver = event.driverUid;
          updates.driverLocation = event.driverLocation;
          break;
        case "DRIVER_ARRIVED":
          updates.driverAwaitingTime = FieldValue.serverTimestamp();
          break;
        case "START_TRIP":
          if (event.userType === "driver") {
            updates.driverStartTime = FieldValue.serverTimestamp();
          } else {
            updates.passengerStartTime = FieldValue.serverTimestamp();
          }

          // Only set tripConfiguration if it doesn't already exist
          if (!tripData.tripConfiguration || Object.keys(tripData.tripConfiguration).length === 0) {
            // If tripConfiguration is provided in the event, use it
            if (event.tripConfiguration && Object.keys(event.tripConfiguration).length > 0) {
              updates.tripConfiguration = event.tripConfiguration;
            } else {
              // Otherwise, fetch the current tenant configuration
              const configDoc = await db.collection(`tenants/${tenantId}/configurations`).doc("tripConfiguration").get();

              if (configDoc.exists) {
                updates.tripConfiguration = configDoc.data();
                logger.info("Trip configuration captured from tenant settings", {
                  tripId,
                  tenantId,
                });
              } else {
                logger.warn("No trip configuration found in tenant settings", {
                  tripId,
                  tenantId,
                });
              }
            }
          } else {
            logger.info("Trip configuration already set, not overwriting", {
              tripId,
              hasConfig: true,
            });
          }
          break;
        case "COMPLETE_TRIP":
          updates.completedAt = FieldValue.serverTimestamp();
          if (event.finalRouteData) {
            updates.routeDataCompleted = event.finalRouteData;
          }
          if (event.costData) {
            updates.costTotal = event.costData.total;
            updates.costDistance = event.costData.distance;
            updates.costTime = event.costData.time;
            updates.distanceTotalMeters = event.costData.distanceMeters;
          }
          break;
        case "CONFIRM_PAYMENT":
          updates.paidAt = FieldValue.serverTimestamp();
          break;
        case "CANCEL":
          updates.cancelledAt = FieldValue.serverTimestamp();
          updates.uidCancelledBy = event.cancelledBy;
          if (event.reason) {
            updates.cancelReason = event.reason;
          }
          break;
      }

      let driverUpdateNeeded = false;
      let driverRef: DocumentReference | null = null;

      // Read driver data first if cancellation involves a driver
      if (event.type === "CANCEL" && tripData.uidChosenDriver) {
        driverRef = db.collection("mobile_users").doc(tripData.uidChosenDriver);
        const driverDoc = await transaction.get(driverRef);
        if (driverDoc.exists) {
          const driverData = driverDoc.data();

          // Check if driver needs to be freed
          if (driverData?.occupiedByTripId === tripId) {
            driverUpdateNeeded = true;
          }
        }
      }

      // Also release driver when trip is completed or paid
      if ((event.type === "COMPLETE_TRIP" || event.type === "CONFIRM_PAYMENT") && tripData.uidChosenDriver) {
        driverRef = db.collection("mobile_users").doc(tripData.uidChosenDriver);
        const driverDoc = await transaction.get(driverRef);
        if (driverDoc.exists) {
          const driverData = driverDoc.data();

          // Check if driver needs to be freed
          if (driverData?.occupiedByTripId === tripId) {
            driverUpdateNeeded = true;
          }
        }
      }

      // Update the trip document
      transaction.update(tripRef, updates);

      // Update driver if needed (for CANCEL, COMPLETE_TRIP, or CONFIRM_PAYMENT events)
      if (driverUpdateNeeded && driverRef) {
        transaction.update(driverRef, { occupiedByTripId: null });

        // Log the reason for driver release
        let releaseReason = "";
        if (event.type === "CANCEL") {
          releaseReason = "trip cancellation";
        } else if (event.type === "COMPLETE_TRIP") {
          releaseReason = "trip completion";
        } else if (event.type === "CONFIRM_PAYMENT") {
          releaseReason = "trip payment confirmation";
        }

        logger.info("Driver released due to " + releaseReason, {
          tripId,
          driverUid: tripData.uidChosenDriver,
          eventType: event.type,
        });
      }

      // Log the state transition
      await logStateTransition(tripId, currentStatus, newState, event, userId, userRole, tenantId, transaction);

      logger.info("State transition completed", {
        tripId,
        previousState: currentStatus,
        newState,
        event: event.type,
        userId,
        userRole,
      });

      // Trigger initial cost calculation when trip starts if costs are missing
      if (event.type === "START_TRIP" && newState === "inProgress" && 
          (tripData.costTotal == null || tripData.estimatedCost == null)) {
        // Run asynchronously after transaction commits
        calculateTripEstimate(tripId, tenantId).catch((error) => {
          logger.error("Failed to calculate initial trip cost", {
            tripId,
            error: error.message,
          });
        });
      }

      // Trigger final cost calculation when trip completes or is cancelled after being in progress
      if (
        (event.type === "COMPLETE_TRIP" && newState === "completed") ||
        (event.type === "CANCEL" && tripData.status === "inProgress" && newState === "cancelled")
      ) {
        // Run asynchronously after transaction commits
        calculateFinalTripCost(tripId, tenantId).catch((error) => {
          logger.error("Failed to calculate final trip cost", {
            tripId,
            error: error.message,
          });
        });
      }

      // Send admin notification for trip cancellation
      if (event.type === "CANCEL" && newState === "cancelled") {
        // Run asynchronously after transaction commits
        sendCancellationNotification(tripId, tripData, event.cancelledBy, userRole, tenantId).catch((error) => {
          logger.error("Failed to send cancellation notification", {
            tripId,
            error: error.message,
          });
        });
      }

      // Send payment notification when trip transitions to paid
      if (event.type === "CONFIRM_PAYMENT" && newState === "paid" && tripData.uidPassenger) {
        // Run asynchronously after transaction commits
        (async () => {
          try {
            // Get passenger data (mobile_users is a global collection)
            const passengerDoc = await db.collection("mobile_users").doc(tripData.uidPassenger).get();
            const passengerData = passengerDoc.data();

            if (passengerData?.fcmToken) {
              await sendPassengerNotification(
                passengerData.fcmToken,
                "trip_paid",
                tripData.uidPassenger,
                { tripId },
                undefined, // no custom body
                undefined, // no custom time
                tenantId
              );
              logger.info("Payment notification sent to passenger ✅", {
                tripId,
                passengerUserId: tripData.uidPassenger,
              });
            } else {
              logger.warn("No FCM token for passenger, payment notification not sent", {
                tripId,
                passengerUserId: tripData.uidPassenger,
              });
            }
          } catch (error: any) {
            logger.error("Failed to send payment notification", {
              tripId,
              error: error.message,
            });
          }
        })();
      }

      return {
        success: true,
        newState,
      };
    });
  } catch (error: any) {
    logger.error("State transition failed", {
      tripId,
      event: event.type,
      error: error.message,
      stack: error.stack,
    });

    throw error;
  }
}

// Firebase Functions
export const transitionTripState = onCall(
  {
    region: "europe-west3",
    timeoutSeconds: 30,
    memory: "512MiB",
  },
  async (request) => {
    if (!request.auth) {
      throw new HttpsError("unauthenticated", "Must be authenticated");
    }

    const { tripId, event, tenantId } = request.data;
    const effectiveTenantId = ensureTenantId(tenantId);
    const userId = request.auth.uid;

    // Determine user role based on trip data
    const tripDoc = await getTenantCollection(effectiveTenantId, "trips").doc(tripId).get();
    if (!tripDoc.exists) {
      throw new HttpsError("not-found", "Trip not found");
    }

    const trip = tripDoc.data()!;
    let userRole: "passenger" | "driver" | "admin" = "passenger";

    if (userId === trip.uidPassenger) {
      userRole = "passenger";
    } else if (userId === trip.uidChosenDriver) {
      userRole = "driver";
    } else {
      // Check if user is admin with tenant access
      const userDoc = await db.collection("admin_users").doc(userId).get();
      if (userDoc.exists && userDoc.data()?.isActive) {
        // Check tenant-specific access
        const tenantAccessDoc = await db.doc(`admin_users/${userId}/tenants/${effectiveTenantId}`).get();

        if (tenantAccessDoc.exists && tenantAccessDoc.data()?.isActive) {
          userRole = "admin";
        } else {
          throw new HttpsError("permission-denied", "Not authorized for this trip");
        }
      } else {
        throw new HttpsError("permission-denied", "Not authorized for this trip");
      }
    }

    return executeStateTransition(tripId, event, userId, userRole, effectiveTenantId);
  }
);

export const requestDriverTransition = onCall(
  {
    region: "europe-west3",
    timeoutSeconds: 30,
    memory: "512MiB",
  },
  async (request) => {
    if (!request.auth) {
      throw new HttpsError("unauthenticated", "Must be authenticated");
    }

    const { tripId, driverUid, driverLocation, tenantId } = request.data;
    const effectiveTenantId = ensureTenantId(tenantId);

    const event: TripEvent = {
      type: "REQUEST_DRIVER",
      driverUid,
      driverLocation,
    };

    return executeStateTransition(tripId, event, request.auth.uid, "passenger", effectiveTenantId);
  }
);

export const driverArrivedTransition = onCall(
  {
    region: "europe-west3",
    timeoutSeconds: 30,
    memory: "512MiB",
  },
  async (request) => {
    if (!request.auth) {
      throw new HttpsError("unauthenticated", "Must be authenticated");
    }

    const { tripId, tenantId } = request.data;
    const effectiveTenantId = ensureTenantId(tenantId);

    const event: TripEvent = {
      type: "DRIVER_ARRIVED",
    };

    return executeStateTransition(tripId, event, request.auth.uid, "driver", effectiveTenantId);
  }
);

export const startTripTransition = onCall(
  {
    region: "europe-west3",
    timeoutSeconds: 30,
    memory: "512MiB",
  },
  async (request) => {
    if (!request.auth) {
      throw new HttpsError("unauthenticated", "Must be authenticated");
    }

    const { tripId, userType, tripConfiguration, tenantId } = request.data;
    const effectiveTenantId = ensureTenantId(tenantId);

    const event: TripEvent = {
      type: "START_TRIP",
      userType,
      tripConfiguration,
    };

    const role = userType === "driver" ? "driver" : "passenger";
    return executeStateTransition(tripId, event, request.auth.uid, role, effectiveTenantId);
  }
);

export const completeTripTransition = onCall(
  {
    region: "europe-west3",
    timeoutSeconds: 30,
    memory: "512MiB",
  },
  async (request) => {
    if (!request.auth) {
      throw new HttpsError("unauthenticated", "Must be authenticated");
    }

    const { tripId, finalRouteData, costData, tenantId } = request.data;
    const effectiveTenantId = ensureTenantId(tenantId);

    const event: TripEvent = {
      type: "COMPLETE_TRIP",
      finalRouteData,
      costData,
    };

    return executeStateTransition(tripId, event, request.auth.uid, "driver", effectiveTenantId);
  }
);

export const cancelTripTransition = onCall(
  {
    region: "europe-west3",
    timeoutSeconds: 30,
    memory: "512MiB",
  },
  async (request) => {
    if (!request.auth) {
      throw new HttpsError("unauthenticated", "Must be authenticated");
    }

    const { tripId, reason, tenantId } = request.data;
    const effectiveTenantId = ensureTenantId(tenantId);

    const event: TripEvent = {
      type: "CANCEL",
      cancelledBy: request.auth.uid,
      reason,
    };

    // Determine user role
    const tripDoc = await getTenantCollection(effectiveTenantId, "trips").doc(tripId).get();
    if (!tripDoc.exists) {
      throw new HttpsError("not-found", "Trip not found");
    }

    const trip = tripDoc.data()!;
    let userRole: "passenger" | "driver" | "admin" = "passenger";

    if (request.auth.uid === trip.uidPassenger) {
      userRole = "passenger";
    } else if (request.auth.uid === trip.uidChosenDriver) {
      userRole = "driver";
    } else {
      const userDoc = await db.collection("admin_users").doc(request.auth.uid).get();
      if (userDoc.exists && userDoc.data()?.isActive) {
        // Check tenant-specific access
        const tenantAccessDoc = await db.doc(`admin_users/${request.auth.uid}/tenants/${effectiveTenantId}`).get();

        if (tenantAccessDoc.exists && tenantAccessDoc.data()?.isActive) {
          userRole = "admin";
        }
      }
    }

    return executeStateTransition(tripId, event, request.auth.uid, userRole, effectiveTenantId);
  }
);

export const dismissTripTransition = onCall(
  {
    region: "europe-west3",
    timeoutSeconds: 30,
    memory: "512MiB",
  },
  async (request) => {
    if (!request.auth) {
      throw new HttpsError("unauthenticated", "Must be authenticated");
    }

    const { tripId, userType, tenantId } = request.data;
    const effectiveTenantId = ensureTenantId(tenantId);

    const event: TripEvent = {
      type: "DISMISS",
      userType,
    };

    const role = userType === "driver" ? "driver" : "passenger";
    return executeStateTransition(tripId, event, request.auth.uid, role, effectiveTenantId);
  }
);

// Admin function to manually override trip cost
export const adminOverrideTripCost = onCall(
  {
    region: "europe-west3",
    timeoutSeconds: 30,
    memory: "512MiB",
    cors: true,
  },
  async (request) => {
    if (!request.auth) {
      throw new HttpsError("unauthenticated", "Must be authenticated");
    }

    const { tripId, newCost, reason, tenantId } = request.data;
    const effectiveTenantId = ensureTenantId(tenantId);
    const adminUid = request.auth.uid;

    // Verify admin permissions
    const userDoc = await db.collection("admin_users").doc(adminUid).get();
    if (!userDoc.exists || !userDoc.data()?.isActive) {
      throw new HttpsError("permission-denied", "Admin access required");
    }

    // Check tenant-specific access
    const tenantAccessDoc = await db.doc(`admin_users/${adminUid}/tenants/${effectiveTenantId}`).get();

    if (!tenantAccessDoc.exists || !tenantAccessDoc.data()?.isActive) {
      throw new HttpsError("permission-denied", "Not authorized for this tenant");
    }

    // Validate input
    if (!tripId || typeof newCost !== "number" || newCost < 0) {
      throw new HttpsError("invalid-argument", "Invalid trip ID or cost amount");
    }

    try {
      const tripRef = getTenantCollection(effectiveTenantId, "trips").doc(tripId);

      await db.runTransaction(async (transaction) => {
        const tripDoc = await transaction.get(tripRef);

        if (!tripDoc.exists) {
          throw new HttpsError("not-found", "Trip not found");
        }

        const tripData = tripDoc.data()!;

        // Only allow override for completed or paid trips
        if (!["completed", "paid"].includes(tripData.status)) {
          throw new HttpsError("failed-precondition", "Can only override cost for completed or paid trips");
        }

        const updates = {
          adminOverrideCost: newCost,
          adminOverrideReason: reason || "Manual admin adjustment",
          adminOverrideByUid: adminUid,
          adminOverrideAt: FieldValue.serverTimestamp(),
          costTotal: newCost, // Update the displayed cost
          lastModified: FieldValue.serverTimestamp(),
        };

        transaction.update(tripRef, updates);

        // Log the override action
        const eventLogRef = getTenantCollection(effectiveTenantId, "event_logs").doc();
        const eventLog = {
          uid: adminUid,
          type: "adminTripCostOverride",
          timestamp: FieldValue.serverTimestamp(),
          timestampDT: new Date(),
          trip: {
            id: tripId,
            originalCost: tripData.costTotal,
            newCost: newCost,
            realCost: tripData.realCost,
          },
          reason: reason || "Manual admin adjustment",
          metadata: {
            adminUid: adminUid,
            previousCost: tripData.costTotal,
            overrideCost: newCost,
          },
        };

        transaction.set(eventLogRef, eventLog);
      });

      logger.info(`Admin ${adminUid} overrode cost for trip ${tripId}: ${newCost} MGA`);

      return {
        success: true,
        message: "Trip cost successfully overridden",
      };
    } catch (error) {
      logger.error(`Error overriding trip cost for ${tripId}:`, error);
      throw error;
    }
  }
);
